import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../domain/auth_state.dart';
import '../domain/app_user.dart';
import 'auth_service.dart';

/// Widget untuk mengelola auth state di aplikasi
/// Menyediakan auth state ke widget tree melalui InheritedWidget
class AuthProvider extends InheritedWidget {
  final AuthService authService;
  final AuthState authState;
  final AppUser? currentUser;
  
  const AuthProvider({
    super.key,
    required this.authService,
    required this.authState,
    required this.currentUser,
    required Widget child,
  }) : super(child: child);
  
  /// Get AuthProvider dari context
  static AuthProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<AuthProvider>();
  }
  
  /// Get AuthProvider dari context (required)
  static AuthProvider requiredOf(BuildContext context) {
    final provider = of(context);
    if (provider == null) {
      throw FlutterError(
        'AuthProvider not found in context. '
        'Make sure your app is wrapped with AuthProvider.',
      );
    }
    return provider;
  }
  
  @override
  bool updateShouldNotify(AuthProvider oldWidget) {
    return authState != oldWidget.authState ||
           currentUser != oldWidget.currentUser;
  }
}

/// Widget builder untuk auth state
class AuthBuilder extends StatefulWidget {
  const AuthBuilder({
    super.key,
    required this.builder,
    this.onAuthStateChanged,
  });
  
  final Widget Function(BuildContext context, AuthState authState) builder;
  final void Function(AuthState authState)? onAuthStateChanged;
  
  @override
  State<AuthBuilder> createState() => _AuthBuilderState();
}

class _AuthBuilderState extends State<AuthBuilder> {
  final Logger _logger = Logger();
  late final AuthService _authService;
  AuthState _currentState = const AuthInitialState();
  
  @override
  void initState() {
    super.initState();
    _authService = AuthService.instance;
    
    // Initialize auth service jika belum
    _initializeAuthService();
    
    // Listen to auth state changes
    _authService.authStateStream.listen(
      (state) {
        if (mounted) {
          setState(() {
            _currentState = state;
          });
          widget.onAuthStateChanged?.call(state);
        }
      },
      onError: (error) {
        _logger.e('Auth state stream error: $error');
        if (mounted) {
          setState(() {
            _currentState = AuthErrorState(error: error.toString());
          });
        }
      },
    );
  }
  
  Future<void> _initializeAuthService() async {
    if (!_authService.isInitialized) {
      try {
        await _authService.initialize();
        if (mounted) {
          setState(() {
            _currentState = _authService.currentAuthState;
          });
        }
      } catch (e) {
        _logger.e('Failed to initialize auth service: $e');
        if (mounted) {
          setState(() {
            _currentState = AuthErrorState(error: 'Failed to initialize authentication');
          });
        }
      }
    } else {
      _currentState = _authService.currentAuthState;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return AuthProvider(
      authService: _authService,
      authState: _currentState,
      currentUser: _authService.isInitialized ? _authService.currentUser : null,
      child: widget.builder(context, _currentState),
    );
  }
}

/// Extension untuk mudah akses auth dari context
extension AuthContextExtension on BuildContext {
  /// Get auth service
  AuthService get authService => AuthProvider.requiredOf(this).authService;
  
  /// Get current auth state
  AuthState get authState => AuthProvider.requiredOf(this).authState;
  
  /// Get current user
  AppUser? get currentUser => AuthProvider.requiredOf(this).currentUser;
  
  /// Check if user is logged in
  bool get isLoggedIn => currentUser != null;
  
  /// Check if user is anonymous
  bool get isAnonymous => currentUser?.isAnonymous ?? false;
  
  /// Check if user has access to feature
  bool hasAccessTo(String feature) => currentUser?.hasAccessTo(feature) ?? false;
  
  /// Get user role display name
  String get userRoleDisplayName => currentUser?.roleDisplayName ?? 'Unknown';
  
  /// Get user display name
  String get userDisplayName => currentUser?.displayName ?? 'Unknown User';
}

/// Mixin untuk StatefulWidget yang membutuhkan auth
mixin AuthMixin<T extends StatefulWidget> on State<T> {
  final Logger _logger = Logger();
  
  /// Get auth service
  AuthService get authService => context.authService;
  
  /// Get current auth state
  AuthState get authState => context.authState;
  
  /// Get current user
  AppUser? get currentUser => context.currentUser;
  
  /// Check if user is logged in
  bool get isLoggedIn => context.isLoggedIn;
  
  /// Check if user is anonymous
  bool get isAnonymous => context.isAnonymous;
  
  /// Check if user has access to feature
  bool hasAccessTo(String feature) => context.hasAccessTo(feature);
  
  /// Handle auth state changes
  @mustCallSuper
  void onAuthStateChanged(AuthState state) {
    _logger.d('Auth state changed: ${state.runtimeType}');
    
    switch (state.runtimeType) {
      case AuthenticatedState:
        onUserSignedIn(state.user!);
        break;
      case AnonymousState:
        onUserSignedInAnonymously(state.user!);
        break;
      case UnauthenticatedState:
        onUserSignedOut();
        break;
      case SessionExpiredState:
        onSessionExpired();
        break;
      case AuthErrorState:
        onAuthError(state.errorMessage!);
        break;
    }
  }
  
  /// Called when user signs in
  void onUserSignedIn(AppUser user) {
    _logger.i('User signed in: ${user.displayName}');
  }
  
  /// Called when user signs in anonymously
  void onUserSignedInAnonymously(AppUser user) {
    _logger.i('User signed in anonymously: ${user.displayName}');
  }
  
  /// Called when user signs out
  void onUserSignedOut() {
    _logger.i('User signed out');
  }
  
  /// Called when session expires
  void onSessionExpired() {
    _logger.w('Session expired');
  }
  
  /// Called when auth error occurs
  void onAuthError(String error) {
    _logger.e('Auth error: $error');
  }
}

/// Widget untuk menampilkan konten berdasarkan auth state
class AuthStateWidget extends StatelessWidget {
  const AuthStateWidget({
    super.key,
    required this.authenticated,
    required this.unauthenticated,
    this.anonymous,
    this.loading,
    this.error,
  });
  
  final Widget authenticated;
  final Widget unauthenticated;
  final Widget? anonymous;
  final Widget? loading;
  final Widget? error;
  
  @override
  Widget build(BuildContext context) {
    final authState = context.authState;
    
    if (authState.isLoading) {
      return loading ?? const Center(child: CircularProgressIndicator());
    }
    
    if (authState.isError) {
      return error ?? Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Authentication Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              authState.errorMessage ?? 'Unknown error occurred',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    if (authState.isAnonymous) {
      return anonymous ?? authenticated;
    }
    
    if (authState.isAuthenticated) {
      return authenticated;
    }
    
    return unauthenticated;
  }
}

/// Widget untuk menampilkan konten berdasarkan user role
class RoleBasedWidget extends StatelessWidget {
  const RoleBasedWidget({
    super.key,
    required this.child,
    this.allowedRoles,
    this.deniedWidget,
    this.allowAnonymous = false,
  });
  
  final Widget child;
  final List<String>? allowedRoles;
  final Widget? deniedWidget;
  final bool allowAnonymous;
  
  @override
  Widget build(BuildContext context) {
    final user = context.currentUser;
    
    // Check if user exists
    if (user == null) {
      return deniedWidget ?? const SizedBox.shrink();
    }
    
    // Check if anonymous is allowed
    if (user.isAnonymous && !allowAnonymous) {
      return deniedWidget ?? const SizedBox.shrink();
    }
    
    // Check if user role is allowed
    if (allowedRoles != null && !allowedRoles!.contains(user.role)) {
      return deniedWidget ?? const SizedBox.shrink();
    }
    
    return child;
  }
}

/// Widget untuk menampilkan konten berdasarkan feature access
class FeatureAccessWidget extends StatelessWidget {
  const FeatureAccessWidget({
    super.key,
    required this.feature,
    required this.child,
    this.deniedWidget,
  });
  
  final String feature;
  final Widget child;
  final Widget? deniedWidget;
  
  @override
  Widget build(BuildContext context) {
    final hasAccess = context.hasAccessTo(feature);
    
    if (!hasAccess) {
      return deniedWidget ?? const SizedBox.shrink();
    }
    
    return child;
  }
}
