import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';
import 'app_button.dart';
import 'app_card.dart';

/// Komponen untuk menampilkan data dalam bentuk list dan table
/// Dirancang khusus untuk kebutuhan operasional dapur dan manajemen data

/// List item yang konsisten untuk berbagai jenis data
class AppListItem extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final String? trailing;
  final Widget? leadingWidget;
  final Widget? trailingWidget;
  final IconData? leadingIcon;
  final IconData? trailingIcon;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool isEnabled;
  final Color? backgroundColor;
  final EdgeInsets? padding;

  const AppListItem({
    super.key,
    this.title,
    this.subtitle,
    this.trailing,
    this.leadingWidget,
    this.trailingWidget,
    this.leadingIcon,
    this.trailingIcon,
    this.onTap,
    this.isSelected = false,
    this.isEnabled = true,
    this.backgroundColor,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.xs),
      child: Material(
        color: backgroundColor ?? 
            (isSelected 
                ? AppColors.primary.withValues(alpha: 0.1) 
                : AppColors.backgroundPrimary),
        borderRadius: BorderRadius.circular(AppRadius.card),
        child: InkWell(
          onTap: isEnabled ? onTap : null,
          borderRadius: BorderRadius.circular(AppRadius.card),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                if (leadingWidget != null) ...[
                  leadingWidget!,
                  const SizedBox(width: AppSpacing.md),
                ] else if (leadingIcon != null) ...[
                  Icon(
                    leadingIcon,
                    color: isSelected 
                        ? AppColors.primary 
                        : AppColors.textTertiary,
                    size: AppIcons.sizeMedium,
                  ),
                  const SizedBox(width: AppSpacing.md),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (title != null)
                        Text(
                          title!,
                          style: AppTypography.bodyMedium.copyWith(
                            color: isEnabled 
                                ? AppColors.textPrimary 
                                : AppColors.textTertiary,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      if (subtitle != null) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          subtitle!,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (trailing != null) ...[
                  const SizedBox(width: AppSpacing.md),
                  Text(
                    trailing!,
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
                if (trailingWidget != null) ...[
                  const SizedBox(width: AppSpacing.md),
                  trailingWidget!,
                ] else if (trailingIcon != null) ...[
                  const SizedBox(width: AppSpacing.md),
                  Icon(
                    trailingIcon,
                    color: AppColors.textTertiary,
                    size: AppIcons.sizeMedium,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Expandable list item untuk menampilkan detail
class AppExpandableListItem extends StatefulWidget {
  final String title;
  final String? subtitle;
  final Widget? leadingWidget;
  final IconData? leadingIcon;
  final Widget child;
  final bool initiallyExpanded;
  final ValueChanged<bool>? onExpansionChanged;

  const AppExpandableListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.leadingWidget,
    this.leadingIcon,
    required this.child,
    this.initiallyExpanded = false,
    this.onExpansionChanged,
  });

  @override
  State<AppExpandableListItem> createState() => _AppExpandableListItemState();
}

class _AppExpandableListItemState extends State<AppExpandableListItem> {
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: ExpansionTile(
        title: Text(
          widget.title,
          style: AppTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: widget.subtitle != null
            ? Text(
                widget.subtitle!,
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textTertiary,
                ),
              )
            : null,
        leading: widget.leadingWidget ??
            (widget.leadingIcon != null
                ? Icon(
                    widget.leadingIcon,
                    color: AppColors.textTertiary,
                    size: AppIcons.sizeMedium,
                  )
                : null),
        initiallyExpanded: _isExpanded,
        onExpansionChanged: (expanded) {
          setState(() {
            _isExpanded = expanded;
          });
          widget.onExpansionChanged?.call(expanded);
        },
        children: [
          Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: widget.child,
          ),
        ],
      ),
    );
  }
}

/// Data table yang responsive dan dapat di-scroll
class AppDataTable extends StatelessWidget {
  final List<AppDataColumn> columns;
  final List<AppDataRow> rows;
  final bool sortAscending;
  final int? sortColumnIndex;
  final ValueChanged<int>? onSort;
  final bool showCheckboxColumn;
  final bool horizontalMargin;
  final double columnSpacing;
  final double dataRowHeight;
  final double headingRowHeight;

  const AppDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.sortAscending = true,
    this.sortColumnIndex,
    this.onSort,
    this.showCheckboxColumn = false,
    this.horizontalMargin = true,
    this.columnSpacing = 56.0,
    this.dataRowHeight = 48.0,
    this.headingRowHeight = 56.0,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: columns
            .map((col) => DataColumn(
                  label: Text(
                    col.label,
                    style: AppTypography.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  numeric: col.numeric,
                  onSort: col.onSort,
                  tooltip: col.tooltip,
                ))
            .toList(),
        rows: rows
            .map((row) => DataRow(
                  selected: row.selected,
                  onSelectChanged: row.onSelectChanged,
                  cells: row.cells
                      .map((cell) => DataCell(
                            cell.child,
                            showEditIcon: cell.showEditIcon,
                            placeholder: cell.placeholder,
                            onTap: cell.onTap,
                          ))
                      .toList(),
                ))
            .toList(),
        sortAscending: sortAscending,
        sortColumnIndex: sortColumnIndex,
        showCheckboxColumn: showCheckboxColumn,
        horizontalMargin: horizontalMargin ? AppSpacing.md : 0,
        columnSpacing: columnSpacing,
        dataRowMinHeight: dataRowHeight,
        dataRowMaxHeight: dataRowHeight * 1.5,
        headingRowHeight: headingRowHeight,
      ),
    );
  }
}

/// Paginated data table untuk data yang besar
class AppPaginatedDataTable extends StatelessWidget {
  final List<AppDataColumn> columns;
  final AppDataTableSource source;
  final int? sortColumnIndex;
  final bool sortAscending;
  final ValueChanged<int>? onSort;
  final int rowsPerPage;
  final List<int> availableRowsPerPage;
  final ValueChanged<int?>? onRowsPerPageChanged;
  final bool showCheckboxColumn;
  final bool showFirstLastButtons;

  const AppPaginatedDataTable({
    super.key,
    required this.columns,
    required this.source,
    this.sortColumnIndex,
    this.sortAscending = true,
    this.onSort,
    this.rowsPerPage = 10,
    this.availableRowsPerPage = const [10, 25, 50, 100],
    this.onRowsPerPageChanged,
    this.showCheckboxColumn = false,
    this.showFirstLastButtons = true,
  });

  @override
  Widget build(BuildContext context) {
    return PaginatedDataTable(
      columns: columns
          .map((col) => DataColumn(
                label: Text(
                  col.label,
                  style: AppTypography.labelMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                numeric: col.numeric,
                onSort: col.onSort,
                tooltip: col.tooltip,
              ))
          .toList(),
      source: source,
      sortColumnIndex: sortColumnIndex,
      sortAscending: sortAscending,
      rowsPerPage: rowsPerPage,
      availableRowsPerPage: availableRowsPerPage,
      onRowsPerPageChanged: onRowsPerPageChanged,
      showCheckboxColumn: showCheckboxColumn,
      showFirstLastButtons: showFirstLastButtons,
    );
  }
}

/// Grid view untuk menampilkan data dalam bentuk grid
class AppGridView extends StatelessWidget {
  final List<Widget> children;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double childAspectRatio;
  final EdgeInsets padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const AppGridView({
    super.key,
    required this.children,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = AppSpacing.md,
    this.mainAxisSpacing = AppSpacing.md,
    this.childAspectRatio = 1.0,
    this.padding = const EdgeInsets.all(AppSpacing.md),
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      childAspectRatio: childAspectRatio,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      children: children,
    );
  }
}

/// Kitchen inventory item dengan status dan actions
class AppKitchenInventoryItem extends StatelessWidget {
  final String itemName;
  final String? description;
  final String quantity;
  final String unit;
  final String? expiryDate;
  final String status;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onView;

  const AppKitchenInventoryItem({
    super.key,
    required this.itemName,
    this.description,
    required this.quantity,
    required this.unit,
    this.expiryDate,
    required this.status,
    this.onEdit,
    this.onDelete,
    this.onView,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        itemName,
                        style: AppTypography.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (description != null) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          description!,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            Row(
              children: [
                Expanded(
                  child: _buildInfoColumn('Jumlah', '$quantity $unit'),
                ),
                if (expiryDate != null)
                  Expanded(
                    child: _buildInfoColumn('Kadaluarsa', expiryDate!),
                  ),
              ],
            ),
            if (onView != null || onEdit != null || onDelete != null) ...[
              const SizedBox(height: AppSpacing.md),
              _buildActionButtons(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.labelSmall.copyWith(
            color: AppColors.textTertiary,
          ),
        ),
        Text(
          value,
          style: AppTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (onView != null)
          AppButtonFactory.outline(
            text: 'Lihat',
            onPressed: onView,
            size: AppButtonSize.small,
          ),
        if (onEdit != null) ...[
          const SizedBox(width: AppSpacing.sm),
          AppButtonFactory.secondary(
            text: 'Edit',
            onPressed: onEdit,
            size: AppButtonSize.small,
          ),
        ],
        if (onDelete != null) ...[
          const SizedBox(width: AppSpacing.sm),
          AppButtonFactory.danger(
            text: 'Hapus',
            onPressed: onDelete,
            size: AppButtonSize.small,
          ),
        ],
      ],
    );
  }

  Widget _buildStatusChip() {
    Color statusColor;
    switch (status.toLowerCase()) {
      case 'available':
        statusColor = AppColors.stockAvailable;
        break;
      case 'low':
        statusColor = AppColors.stockLow;
        break;
      case 'empty':
        statusColor = AppColors.stockEmpty;
        break;
      case 'expiring':
        statusColor = AppColors.stockExpiring;
        break;
      default:
        statusColor = AppColors.neutralGray500;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.sm),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        status,
        style: AppTypography.labelSmall.copyWith(
          color: statusColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

/// Loading list untuk skeleton loading
class AppLoadingList extends StatelessWidget {
  final int itemCount;
  final double itemHeight;

  const AppLoadingList({
    super.key,
    this.itemCount = 10,
    this.itemHeight = 80.0,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Container(
          height: itemHeight,
          margin: const EdgeInsets.only(bottom: AppSpacing.sm),
          child: AppCard(
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppColors.neutralGray200,
                      borderRadius: BorderRadius.circular(AppRadius.sm),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 16,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: AppColors.neutralGray200,
                            borderRadius: BorderRadius.circular(AppRadius.sm),
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Container(
                          height: 14,
                          width: 200,
                          decoration: BoxDecoration(
                            color: AppColors.neutralGray200,
                            borderRadius: BorderRadius.circular(AppRadius.sm),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Model untuk data column
class AppDataColumn {
  final String label;
  final bool numeric;
  final DataColumnSortCallback? onSort;
  final String? tooltip;

  const AppDataColumn({
    required this.label,
    this.numeric = false,
    this.onSort,
    this.tooltip,
  });
}

/// Model untuk data row
class AppDataRow {
  final List<AppDataCell> cells;
  final bool selected;
  final ValueChanged<bool?>? onSelectChanged;

  const AppDataRow({
    required this.cells,
    this.selected = false,
    this.onSelectChanged,
  });
}

/// Model untuk data cell
class AppDataCell {
  final Widget child;
  final bool showEditIcon;
  final bool placeholder;
  final VoidCallback? onTap;

  const AppDataCell({
    required this.child,
    this.showEditIcon = false,
    this.placeholder = false,
    this.onTap,
  });
}

/// Abstract class untuk data table source
abstract class AppDataTableSource extends DataTableSource {
  @override
  DataRow? getRow(int index);

  @override
  int get rowCount;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
}

/// Data list builder untuk menampilkan daftar data dengan loading state
class AppDataListBuilder<T> extends StatelessWidget {
  final List<T>? data;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final Widget? errorWidget;
  final String? error;
  final ScrollController? scrollController;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const AppDataListBuilder({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.loadingWidget,
    this.emptyWidget,
    this.errorWidget,
    this.error,
    this.scrollController,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    if (error != null) {
      return errorWidget ?? _buildDefaultError(error!);
    }

    if (data == null) {
      return loadingWidget ?? const AppLoadingList();
    }

    if (data!.isEmpty) {
      return emptyWidget ?? _buildDefaultEmpty();
    }

    return ListView.builder(
      controller: scrollController,
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      shrinkWrap: shrinkWrap,
      physics: physics,
      itemCount: data!.length,
      itemBuilder: (context, index) {
        return itemBuilder(context, data![index], index);
      },
    );
  }

  Widget _buildDefaultError(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            AppIcons.error,
            size: 48,
            color: AppColors.errorRed,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Terjadi Kesalahan',
            style: AppTypography.h6.copyWith(
              color: AppColors.errorRed,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            errorMessage,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            AppIcons.boxes,
            size: 48,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Tidak Ada Data',
            style: AppTypography.h6.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Belum ada data untuk ditampilkan',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Search bar untuk filtering data
class AppSearchBar extends StatelessWidget {
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final String? value;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  const AppSearchBar({
    super.key,
    this.hintText,
    this.onChanged,
    this.onClear,
    this.value,
    this.enabled = true,
    this.prefixIcon,
    this.suffixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppRadius.input),
        border: Border.all(
          color: AppColors.neutralGray200,
        ),
      ),
      child: Row(
        children: [
          prefixIcon ?? 
          Icon(
            AppIcons.search,
            color: AppColors.textTertiary,
            size: AppIcons.sizeMedium,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: TextField(
              enabled: enabled,
              onChanged: onChanged,
              controller: value != null ? TextEditingController(text: value) : null,
              decoration: InputDecoration(
                hintText: hintText ?? 'Cari...',
                border: InputBorder.none,
                hintStyle: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textTertiary,
                ),
              ),
              style: AppTypography.bodyMedium,
            ),
          ),
          if (value?.isNotEmpty == true && onClear != null) ...[
            const SizedBox(width: AppSpacing.sm),
            InkWell(
              onTap: onClear,
              borderRadius: BorderRadius.circular(AppRadius.sm),
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.xs),
                child: Icon(
                  AppIcons.close,
                  color: AppColors.textTertiary,
                  size: AppIcons.sizeSmall,
                ),
              ),
            ),
          ],
          if (suffixIcon != null) ...[
            const SizedBox(width: AppSpacing.sm),
            suffixIcon!,
          ],
        ],
      ),
    );
  }
}
