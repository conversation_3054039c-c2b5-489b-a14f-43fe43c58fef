import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_form.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/app_notifications.dart';
import '../../../../app/widgets/responsive_layout.dart';
import '../../../../app/config/app_router.dart';
import '../../../../core/auth/presentation/auth_widgets.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../../../core/auth/presentation/auth_mode_indicator.dart';
import '../../../../core/auth/domain/auth_state.dart';

/// Halaman registrasi untuk Aplikasi SOD-MBG
/// Menampilkan form registrasi dengan design system yang konsisten
class RegistrationPage extends StatefulWidget {
  const RegistrationPage({super.key});

  @override
  State<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> 
    with AuthMixin<RegistrationPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _namaController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _sppgNameController = TextEditingController();
  final _namaYayasanController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String _selectedRole = 'admin_yayasan'; // Default role is admin yayasan
  bool _acceptTerms = false;

  @override
  void dispose() {
    _namaController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _sppgNameController.dispose();
    _namaYayasanController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          const AuthModeBanner(),
          Expanded(
            child: AuthBuilder(
              builder: (context, authState) {
                return Container(
                  decoration: _buildBackgroundDecoration(),
                  child: ResponsiveLayout(
                    mobile: _buildMobileLayout(),
                    tablet: _buildTabletLayout(),
                    desktop: _buildDesktopLayout(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primary,
          AppColors.primaryLight,
          AppColors.secondary,
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Welcome section
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.xxl),
            child: _buildWelcomeSection(),
          ),
        ),
        // Right side - Registration form
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.xxl),
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 500),
                child: _buildRegistrationForm(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Column(
            children: [
              _buildWelcomeSection(),
              const SizedBox(height: AppSpacing.xl),
              _buildRegistrationForm(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            _buildWelcomeSection(),
            const SizedBox(height: AppSpacing.xl),
            _buildRegistrationForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Logo and title
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.neutralWhite,
                borderRadius: BorderRadius.circular(radius.AppRadius.lg),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.neutralGray300.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                AppIcons.kitchen,
                size: 40,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h2.copyWith(
                      color: AppColors.neutralWhite,
                      fontWeight: AppTypography.bold,
                    ),
                  ),
                  Text(
                    'Sistem Operasional Dapur',
                    style: AppTypography.bodyLarge.copyWith(
                      color: AppColors.neutralWhite.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xl),
        
        // Welcome text
        Text(
          'Pendaftaran Admin Yayasan',
          style: AppTypography.h3.copyWith(
            color: AppColors.neutralWhite,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Daftar sebagai Administrator Yayasan untuk mengelola sistem MBG dengan akses penuh ke semua fitur operasional.',
          style: AppTypography.bodyLarge.copyWith(
            color: AppColors.neutralWhite.withOpacity(0.9),
            height: 1.5,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        
        // Features list
        _buildFeatureItem(
          icon: AppIcons.adminYayasan,
          title: 'Akses Penuh Sistem',
          description: 'Kelola semua aspek operasional MBG',
        ),
        const SizedBox(height: AppSpacing.md),
        _buildFeatureItem(
          icon: AppIcons.analytics,
          title: 'Dashboard Eksekutif',
          description: 'Pantau performa seluruh SPPG',
        ),
        const SizedBox(height: AppSpacing.md),
        _buildFeatureItem(
          icon: AppIcons.userGroup,
          title: 'Manajemen Organisasi',
          description: 'Kelola yayasan dan SPPG di bawahnya',
        ),
      ],
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: AppColors.neutralWhite.withOpacity(0.2),
            borderRadius: BorderRadius.circular(radius.AppRadius.sm),
          ),
          child: Icon(
            icon,
            size: 20,
            color: AppColors.neutralWhite,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.neutralWhite,
                  fontWeight: AppTypography.semiBold,
                ),
              ),
              Text(
                description,
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.neutralWhite.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRegistrationForm() {
    return AppCardFactory.basic(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Text(
              'Daftar Admin Yayasan',
              style: AppTypography.h4.copyWith(
                color: AppColors.textPrimary,
                fontWeight: AppTypography.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Lengkapi formulir untuk mendaftar sebagai Administrator Yayasan',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.xl),

            // Role indicator (read-only)
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(radius.AppRadius.md),
                border: Border.all(color: AppColors.primary.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    AppIcons.adminYayasan,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Administrator Yayasan',
                          style: AppTypography.bodyMedium.copyWith(
                            color: AppColors.primary,
                            fontWeight: AppTypography.semiBold,
                          ),
                        ),
                        Text(
                          'Akses penuh ke semua fitur sistem operasional',
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppSpacing.lg),

            // Organization Information
            Text(
              'Informasi Yayasan',
              style: AppTypography.h6.copyWith(
                color: AppColors.textPrimary,
                fontWeight: AppTypography.semiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Nama Yayasan field
            AppFormField(
              controller: _namaYayasanController,
              label: 'Nama Yayasan',
              hint: 'Masukkan nama yayasan Anda',
              prefixIcon: AppIcons.adminYayasan,
              isRequired: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Nama yayasan wajib diisi';
                }
                if (value.length < 3) {
                  return 'Nama yayasan minimal 3 karakter';
                }
                return null;
              },
            ),
            const SizedBox(height: AppSpacing.lg),

            // Personal Information
            Text(
              'Informasi Personal',
              style: AppTypography.h6.copyWith(
                color: AppColors.textPrimary,
                fontWeight: AppTypography.semiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Nama field
            AppFormField(
              controller: _namaController,
              label: 'Nama Lengkap',
              hint: 'Masukkan nama lengkap Anda',
              prefixIcon: AppIcons.user,
              isRequired: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Nama lengkap wajib diisi';
                }
                if (value.length < 3) {
                  return 'Nama minimal 3 karakter';
                }
                return null;
              },
            ),
            const SizedBox(height: AppSpacing.md),

            // Email field
            AppFormField(
              controller: _emailController,
              label: 'Email',
              hint: 'Masukkan alamat email',
              prefixIcon: AppIcons.email,
              keyboardType: TextInputType.emailAddress,
              isRequired: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Email wajib diisi';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return 'Format email tidak valid';
                }
                return null;
              },
            ),
            const SizedBox(height: AppSpacing.md),

            // SPPG Name field - Not needed for admin yayasan
            // Admin yayasan manages multiple SPPG, so no specific SPPG assignment

            // Password field
            AppFormField(
              controller: _passwordController,
              label: 'Password',
              hint: 'Masukkan password',
              prefixIcon: AppIcons.security,
              obscureText: _obscurePassword,
              isRequired: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Password wajib diisi';
                }
                if (value.length < 6) {
                  return 'Password minimal 6 karakter';
                }
                return null;
              },
            ),
            const SizedBox(height: AppSpacing.md),

            // Confirm Password field
            AppFormField(
              controller: _confirmPasswordController,
              label: 'Konfirmasi Password',
              hint: 'Masukkan ulang password',
              prefixIcon: AppIcons.security,
              obscureText: _obscureConfirmPassword,
              isRequired: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Konfirmasi password wajib diisi';
                }
                if (value != _passwordController.text) {
                  return 'Password tidak cocok';
                }
                return null;
              },
            ),
            const SizedBox(height: AppSpacing.lg),

            // Terms and conditions
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Checkbox(
                  value: _acceptTerms,
                  onChanged: (value) {
                    setState(() {
                      _acceptTerms = value ?? false;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _acceptTerms = !_acceptTerms;
                      });
                    },
                    child: Text.rich(
                      TextSpan(
                        text: 'Saya setuju dengan ',
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        children: [
                          TextSpan(
                            text: 'Syarat dan Ketentuan',
                            style: AppTypography.bodySmall.copyWith(
                              color: AppColors.primary,
                              fontWeight: AppTypography.semiBold,
                            ),
                          ),
                          TextSpan(
                            text: ' serta ',
                            style: AppTypography.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          TextSpan(
                            text: 'Kebijakan Privasi',
                            style: AppTypography.bodySmall.copyWith(
                              color: AppColors.primary,
                              fontWeight: AppTypography.semiBold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.xl),

            // Register button
            AppButtonFactory.primary(
              text: 'Daftar Akun',
              onPressed: _acceptTerms ? _handleRegister : null,
              isLoading: _isLoading,
              isFullWidth: true,
            ),
            const SizedBox(height: AppSpacing.lg),

            // Login link
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Sudah punya akun? ',
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    AppRouter.goToLogin(context);
                  },
                  child: Text(
                    'Masuk di sini',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: AppTypography.semiBold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_acceptTerms) {
      AppNotifications.showSnackBar(
        context,
        message: 'Silakan setujui syarat dan ketentuan untuk melanjutkan',
        type: NotificationType.warning,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      _logger.d('Starting registration process for admin yayasan');
      
      // Check if auth service is initialized
      final authService = AuthService.instance;
      if (!authService.isInitialized) {
        throw Exception('Authentication service is not available. Please check your configuration.');
      }
      
      // Show warning if using mock authentication
      if (authService.isMockMode) {
        _logger.w('Using mock authentication - registration will be simulated');
      }
      
      // Call auth service to register
      final authState = await authService.signUpWithEmail(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        nama: _namaController.text.trim(),
        role: _selectedRole, // Always 'admin_yayasan'
        sppgName: _namaYayasanController.text.trim(), // Using sppgName for yayasan name
      );

      if (authState is AuthenticatedState) {
        _logger.i('Registration successful for admin yayasan: ${authState.user.nama}');
        
        // Show success message
        AppNotifications.showSnackBar(
          context,
          message: 'Akun Admin Yayasan berhasil dibuat! Selamat datang, ${authState.user.nama}',
          type: NotificationType.success,
        );

        // Navigate to dashboard
        AppRouter.goToDashboard(context);
      } else if (authState is AuthErrorState) {
        _logger.w('Registration failed: ${authState.error}');
        
        // Show error message
        AppNotifications.showSnackBar(
          context,
          message: authState.error,
          type: NotificationType.error,
        );
      }
    } catch (e) {
      _logger.e('Registration error: $e');
      
      // Show generic error message
      AppNotifications.showSnackBar(
        context,
        message: 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.',
        type: NotificationType.error,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
