# Sistem Operasional Dapur MBG (SOD-MBG)

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
![Platform](https://img.shields.io/badge/platform-Flutter%20%7C%20Android%20%7C%20iOS-blue)
![License](https://img.shields.io/badge/license-MIT-lightgrey)

Sistem Operasional Dapur MBG (SOD-MBG) adalah aplikasi mobile *cross-platform* yang dirancang khusus untuk mendukung operasional harian Dapur Satuan Pelayanan Pemenuhan Gizi (SPPG) dalam menjalankan Program Makan Bergizi Gratis (MBG) dari Badan Gizi Nasional (BGN).

Aplikasi ini bertujuan untuk meningkatkan efisiensi internal, memastikan akurasi pelaporan, dan menyediakan alat pengawasan yang ketat bagi yayasan terhadap Dapur SPPG, baik yang dimiliki sendiri maupun yang berstatus sebagai mitra non-yayasan.

## Daftar Isi
1.  [Fitur Utama](#fitur-utama)
2.  [<PERSON><PERSON> & <PERSON>](#peran-pengguna--hak-akses)
3.  [Tumpukan Teknologi (Tech Stack)](#tumpukan-teknologi-tech-stack)
4.  [Memulai Proyek](#memulai-proyek)
    * [Prasyarat](#prasyarat)
    * [Instalasi](#instalasi)
5.  [Struktur Proyek](#struktur-proyek)
6.  [Pedoman Kontribusi](#pedoman-kontribusi)
7.  [Lisensi](#lisensi)

## Fitur Utama

-   **Dashboard Terpusat:** Monitoring performa agregat dan individual dari semua Dapur SPPG.
-   **Manajemen Operasional Dapur:** Pengelolaan alur kerja harian dari hulu ke hilir, termasuk perencanaan menu, manajemen produksi, dan kontrol kualitas (QC).
-   **Manajemen Inventaris (Offline-First):** Pencatatan real-time untuk penerimaan dan penggunaan bahan baku, bahkan tanpa koneksi internet.
-   **Logistik & Distribusi:** Pelacakan pengiriman dengan bukti foto dan GPS, serta manajemen armada dan alat makan.
-   **Manajemen Keuangan:** Pencatatan transaksi, unggah bukti bayar, dan pembuatan laporan keuangan per SPPG.
-   **Pelaporan Akurat:** Generasi laporan harian, mingguan, dan bulanan yang siap untuk dianalisis atau diunggah ke Portal Mitra BGN.
-   **Manajemen Peran & Pengguna:** Kontrol akses yang ketat berdasarkan peran spesifik setiap pengguna.

## Peran Pengguna & Hak Akses

Aplikasi ini mengimplementasikan Role-Based Access Control (RBAC) untuk 6 peran utama:

1.  **Admin Yayasan:** Akses penuh ke seluruh sistem. Mengelola SPPG, pengguna, dan persetujuan final laporan.
2.  **Perwakilan Yayasan:** Pengawas operasional. Memverifikasi laporan harian, melakukan audit digital, dan mengelola insiden.
3.  **Kepala Dapur SPPG:** Manajer dapur. Mengelola operasional harian, staf, QC, dan menyusun laporan harian.
4.  **Ahli Gizi:** Penanggung jawab menu dan gizi. Mengelola siklus menu dan memvalidasi kesesuaian gizi harian.
5.  **Akuntan:** Penanggung jawab keuangan. Mencatat transaksi, melakukan rekonsiliasi, dan menyusun laporan keuangan SPPG.
6.  **Pengawas Pemeliharaan & Penghantaran:** Ujung tombak logistik. Mengelola distribusi, armada, dan pelaporan pemeliharaan.

## Tumpukan Teknologi (Tech Stack)

-   **Framework Aplikasi:** [**Flutter**](https://flutter.dev/) - Untuk membangun aplikasi Android dan iOS dari satu basis kode.
-   **Backend & Database:** [**Supabase**](https://supabase.io/) (Alternatif Firebase berbasis open-source).
    -   **Database:** [**PostgreSQL**](https://www.postgresql.org/)
    -   **Authentication:** Sup