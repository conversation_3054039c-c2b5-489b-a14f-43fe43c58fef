import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';
import 'app_button.dart';

/// Komponen form yang konsisten untuk seluruh aplikasi
/// Menyediakan berbagai jenis input field dengan styling yang seragam
class AppFormField extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helper;
  final String? error;
  final bool isRequired;
  final bool isEnabled;
  final bool isReadOnly;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final TextEditingController? controller;
  final String? initialValue;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onFieldSubmitted;
  final FormFieldValidator<String>? validator;
  final Widget? prefix;
  final Widget? suffix;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixTap;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final bool showCounter;
  final AutovalidateMode? autovalidateMode;
  final FocusNode? focusNode;

  const AppFormField({
    super.key,
    this.label,
    this.hint,
    this.helper,
    this.error,
    this.isRequired = false,
    this.isEnabled = true,
    this.isReadOnly = false,
    this.obscureText = false,
    this.keyboardType,
    this.textInputAction,
    this.inputFormatters,
    this.controller,
    this.initialValue,
    this.onChanged,
    this.onTap,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.validator,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixTap,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.showCounter = true,
    this.autovalidateMode,
    this.focusNode,
  });

  @override
  State<AppFormField> createState() => _AppFormFieldState();
}

class _AppFormFieldState extends State<AppFormField> {
  late bool _obscureText;
  late FocusNode _focusNode;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    if (mounted) {
      setState(() {
        _hasFocus = _focusNode.hasFocus;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          _buildLabel(),
          const SizedBox(height: AppSpacing.sm),
        ],
        _buildTextField(),
        if (widget.helper != null || widget.error != null) ...[
          const SizedBox(height: AppSpacing.xs),
          _buildHelperOrError(),
        ],
      ],
    );
  }

  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        text: widget.label!,
        style: AppTypography.bodyMedium.copyWith(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w500,
        ),
        children: [
          if (widget.isRequired)
            TextSpan(
              text: ' *',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.errorRed,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextField() {
    return TextFormField(
      controller: widget.controller,
      initialValue: widget.initialValue,
      focusNode: _focusNode,
      enabled: widget.isEnabled,
      readOnly: widget.isReadOnly,
      obscureText: _obscureText,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      inputFormatters: widget.inputFormatters,
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      onEditingComplete: widget.onEditingComplete,
      onFieldSubmitted: widget.onFieldSubmitted,
      validator: widget.validator,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      autovalidateMode: widget.autovalidateMode,
      style: AppTypography.bodyMedium,
      decoration: InputDecoration(
        hintText: widget.hint,
        hintStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textTertiary,
        ),
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                color: _hasFocus ? AppColors.primary : AppColors.textTertiary,
                size: AppIcons.sizeMedium,
              )
            : null,
        prefix: widget.prefix,
        suffixIcon: _buildSuffixIcon(),
        suffix: widget.suffix,
        filled: true,
        fillColor: widget.isEnabled 
            ? AppColors.backgroundSecondary 
            : AppColors.neutralGray100,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
          borderSide: BorderSide(
            color: AppColors.neutralGray300,
            width: 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
          borderSide: BorderSide(
            color: AppColors.neutralGray300,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
          borderSide: BorderSide(
            color: AppColors.primary,
            width: 2.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
          borderSide: BorderSide(
            color: AppColors.errorRed,
            width: 1.0,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
          borderSide: BorderSide(
            color: AppColors.errorRed,
            width: 2.0,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
          borderSide: BorderSide(
            color: AppColors.neutralGray200,
            width: 1.0,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: widget.maxLines == 1 ? AppSpacing.sm : AppSpacing.md,
        ),
        counterStyle: AppTypography.labelSmall.copyWith(
          color: AppColors.textTertiary,
        ),
        counterText: widget.showCounter ? null : '',
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.obscureText) {
      return IconButton(
        icon: Icon(
          _obscureText ? AppIcons.visibility : AppIcons.visibilityOff,
          color: AppColors.textTertiary,
          size: AppIcons.sizeMedium,
        ),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    }

    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(
          widget.suffixIcon,
          color: _hasFocus ? AppColors.primary : AppColors.textTertiary,
          size: AppIcons.sizeMedium,
        ),
        onPressed: widget.onSuffixTap,
      );
    }

    return null;
  }

  Widget _buildHelperOrError() {
    if (widget.error != null) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            AppIcons.error,
            color: AppColors.errorRed,
            size: AppIcons.sizeSmall,
          ),
          const SizedBox(width: AppSpacing.xs),            Expanded(
              child: Text(
                widget.error!,
                style: AppTypography.labelSmall.copyWith(
                  color: AppColors.errorRed,
                ),
              ),
            ),
        ],
      );
    }

    if (widget.helper != null) {
      return Text(
        widget.helper!,
        style: AppTypography.labelSmall.copyWith(
          color: AppColors.textTertiary,
        ),
      );
    }

    return const SizedBox.shrink();
  }
}

/// Dropdown field yang konsisten
class AppDropdownField<T> extends StatelessWidget {
  final String? label;
  final String? hint;
  final String? helper;
  final String? error;
  final bool isRequired;
  final bool isEnabled;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final FormFieldValidator<T>? validator;
  final Widget? prefix;
  final IconData? prefixIcon;
  final AutovalidateMode? autovalidateMode;

  const AppDropdownField({
    super.key,
    this.label,
    this.hint,
    this.helper,
    this.error,
    this.isRequired = false,
    this.isEnabled = true,
    this.value,
    required this.items,
    this.onChanged,
    this.validator,
    this.prefix,
    this.prefixIcon,
    this.autovalidateMode,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          _buildLabel(),
          const SizedBox(height: AppSpacing.sm),
        ],
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: isEnabled ? onChanged : null,
          validator: validator,
          autovalidateMode: autovalidateMode,
          style: AppTypography.bodyMedium,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTypography.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.textTertiary,
                    size: AppIcons.sizeMedium,
                  )
                : null,
            prefix: prefix,
            filled: true,
            fillColor: isEnabled 
                ? AppColors.backgroundSecondary 
                : AppColors.neutralGray100,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.input),
              borderSide: BorderSide(
                color: AppColors.neutralGray300,
                width: 1.0,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.input),
              borderSide: BorderSide(
                color: AppColors.neutralGray300,
                width: 1.0,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.input),
              borderSide: BorderSide(
                color: AppColors.primary,
                width: 2.0,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.input),
              borderSide: BorderSide(
                color: AppColors.errorRed,
                width: 1.0,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.input),
              borderSide: BorderSide(
                color: AppColors.errorRed,
                width: 2.0,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.input),
              borderSide: BorderSide(
                color: AppColors.neutralGray200,
                width: 1.0,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
          ),
        ),
        if (helper != null || error != null) ...[
          const SizedBox(height: AppSpacing.xs),
          _buildHelperOrError(),
        ],
      ],
    );
  }

  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        text: label!,
        style: AppTypography.bodyMedium.copyWith(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w500,
        ),
        children: [
          if (isRequired)
            TextSpan(
              text: ' *',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.errorRed,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHelperOrError() {
    if (error != null) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            AppIcons.error,
            color: AppColors.errorRed,
            size: AppIcons.sizeSmall,
          ),
          const SizedBox(width: AppSpacing.xs),
          Expanded(
            child: Text(
              error!,
              style: AppTypography.labelSmall.copyWith(
                color: AppColors.errorRed,
              ),
            ),
          ),
        ],
      );
    }

    if (helper != null) {
      return Text(
        helper!,
        style: AppTypography.labelSmall.copyWith(
          color: AppColors.textTertiary,
        ),
      );
    }

    return const SizedBox.shrink();
  }
}

/// Checkbox field yang konsisten
class AppCheckboxField extends StatelessWidget {
  final String? label;
  final String? description;
  final bool value;
  final ValueChanged<bool?>? onChanged;
  final bool isEnabled;
  final bool isRequired;
  final String? error;

  const AppCheckboxField({
    super.key,
    this.label,
    this.description,
    required this.value,
    this.onChanged,
    this.isEnabled = true,
    this.isRequired = false,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: isEnabled ? () => onChanged?.call(!value) : null,
          borderRadius: BorderRadius.circular(AppRadius.sm),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.xs),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Checkbox(
                  value: value,
                  onChanged: isEnabled ? onChanged : null,
                  activeColor: AppColors.primary,
                  checkColor: AppColors.textOnPrimary,
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (label != null)
                        RichText(
                          text: TextSpan(
                            text: label!,
                            style: AppTypography.bodyMedium.copyWith(
                              color: isEnabled 
                                  ? AppColors.textPrimary 
                                  : AppColors.textTertiary,
                            ),
                            children: [
                              if (isRequired)
                                TextSpan(
                                  text: ' *',
                                  style: AppTypography.bodyMedium.copyWith(
                                    color: AppColors.errorRed,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      if (description != null) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          description!,
                          style: AppTypography.labelSmall.copyWith(
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        if (error != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                AppIcons.error,
                color: AppColors.errorRed,
                size: AppIcons.sizeSmall,
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: Text(
                  error!,
                  style: AppTypography.caption.copyWith(
                    color: AppColors.errorRed,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

/// Radio button field yang konsisten
class AppRadioField<T> extends StatelessWidget {
  final String? label;
  final List<AppRadioOption<T>> options;
  final T? value;
  final ValueChanged<T?>? onChanged;
  final bool isEnabled;
  final bool isRequired;
  final String? error;
  final bool isVertical;

  const AppRadioField({
    super.key,
    this.label,
    required this.options,
    this.value,
    this.onChanged,
    this.isEnabled = true,
    this.isRequired = false,
    this.error,
    this.isVertical = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          RichText(
            text: TextSpan(
              text: label!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              children: [
                if (isRequired)
                  TextSpan(
                    text: ' *',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.errorRed,
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
        ],
        if (isVertical)
          Column(
            children: options.map((option) => _buildRadioOption(option)).toList(),
          )
        else
          Wrap(
            spacing: AppSpacing.md,
            children: options.map((option) => _buildRadioOption(option)).toList(),
          ),
        if (error != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                AppIcons.error,
                color: AppColors.errorRed,
                size: AppIcons.sizeSmall,
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: Text(
                  error!,
                  style: AppTypography.caption.copyWith(
                    color: AppColors.errorRed,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildRadioOption(AppRadioOption<T> option) {
    return InkWell(
      onTap: isEnabled ? () => onChanged?.call(option.value) : null,
      borderRadius: BorderRadius.circular(AppRadius.sm),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xs),
        child: Row(
          mainAxisSize: isVertical ? MainAxisSize.max : MainAxisSize.min,
          children: [
            Radio<T>(
              value: option.value,
              groupValue: value,
              onChanged: isEnabled ? onChanged : null,
              activeColor: AppColors.primary,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              flex: isVertical ? 1 : 0,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.label,
                    style: AppTypography.bodyMedium.copyWith(
                      color: isEnabled 
                          ? AppColors.textPrimary 
                          : AppColors.textTertiary,
                    ),
                  ),
                  if (option.description != null) ...[
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      option.description!,
                      style: AppTypography.labelSmall.copyWith(
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Model untuk radio option
class AppRadioOption<T> {
  final T value;
  final String label;
  final String? description;

  const AppRadioOption({
    required this.value,
    required this.label,
    this.description,
  });
}

/// Form section dengan title dan divider
class AppFormSection extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget> children;
  final bool showDivider;

  const AppFormSection({
    super.key,
    required this.title,
    this.subtitle,
    required this.children,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showDivider) ...[
          const Divider(color: AppColors.neutralGray300),
          const SizedBox(height: AppSpacing.lg),
        ],
        Text(
          title,
          style: AppTypography.h6,
        ),
        if (subtitle != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            subtitle!,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
        const SizedBox(height: AppSpacing.lg),
        ...children,
      ],
    );
  }
}

/// Form action buttons
class AppFormActions extends StatelessWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;
  final VoidCallback? onSubmit;
  final String? cancelText;
  final String? saveText;
  final String? submitText;
  final bool isLoading;
  final bool isEnabled;

  const AppFormActions({
    super.key,
    this.onCancel,
    this.onSave,
    this.onSubmit,
    this.cancelText,
    this.saveText,
    this.submitText,
    this.isLoading = false,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        children: [
          if (onCancel != null) ...[
            Expanded(
              child: AppButtonFactory.outline(
                text: cancelText ?? 'Batal',
                onPressed: isEnabled && !isLoading ? onCancel : null,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
          ],
          if (onSave != null) ...[
            Expanded(
              child: AppButtonFactory.secondary(
                text: saveText ?? 'Simpan',
                onPressed: isEnabled && !isLoading ? onSave : null,
                isLoading: isLoading,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
          ],
          if (onSubmit != null)
            Expanded(
              child: AppButtonFactory.primary(
                text: submitText ?? 'Kirim',
                onPressed: isEnabled && !isLoading ? onSubmit : null,
                isLoading: isLoading,
              ),
            ),
        ],
      ),
    );
  }
}
