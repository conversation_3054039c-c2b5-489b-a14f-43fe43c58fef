import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/app_form.dart';
import '../../../../app/widgets/app_notifications.dart';

/// Halaman QC Harian untuk Test Food
/// Khusus untuk Kepala Dapur SPPG melakukan quality control sebelum makanan didistribusikan
class QcDailyPage extends StatefulWidget {
  const QcDailyPage({super.key});

  @override
  State<QcDailyPage> createState() => _QcDailyPageState();
}

class _QcDailyPageState extends State<QcDailyPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _notesController = TextEditingController();
  
  bool _isLoading = false;
  bool _testFoodApproved = false;
  
  // Data menu hari ini (dummy data)
  final Map<String, dynamic> _todayMenu = {
    'tanggal': '2024-12-31',
    'menuUtama': 'Nasi Putih + Ayam Gulai',
    'sayuran': 'Sayur Asem',
    'buah': 'Pisang',
    'minuman': 'Air Putih',
    'totalPorsi': 750,
    'waktuMasak': '06:00 - 08:00',
    'waktuDistribusi': '10:30 - 12:00',
  };

  @override
  void initState() {
    super.initState();
    _logger.i('QC Daily Page initialized');
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('QC Harian - Test Food'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMenuInfoCard(),
              const SizedBox(height: AppSpacing.lg),
              _buildQcChecklist(),
              const SizedBox(height: AppSpacing.lg),
              _buildNotesSection(),
              const SizedBox(height: AppSpacing.xl),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuInfoCard() {
    return AppCardFactory.header(
      title: 'Menu Hari Ini',
      subtitle: _todayMenu['tanggal'],
      leading: Icon(
        AppIcons.menuBook,
        color: AppColors.primary,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.sm),
          _buildMenuDetail('Menu Utama', _todayMenu['menuUtama']),
          _buildMenuDetail('Sayuran', _todayMenu['sayuran']),
          _buildMenuDetail('Buah', _todayMenu['buah']),
          _buildMenuDetail('Minuman', _todayMenu['minuman']),
          const SizedBox(height: AppSpacing.md),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total Porsi',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    '${_todayMenu['totalPorsi']} porsi',
                    style: AppTypography.h6.copyWith(
                      color: AppColors.primary,
                      fontWeight: AppTypography.bold,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Waktu Distribusi',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    _todayMenu['waktuDistribusi'],
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: AppTypography.semiBold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMenuDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQcChecklist() {
    return AppCardFactory.header(
      title: 'Test Food Quality Control',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Pastikan semua item berikut sudah diperiksa sebelum memberikan persetujuan:',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          _buildChecklistItem('Rasa makanan sesuai standar'),
          _buildChecklistItem('Aroma makanan tidak menyengat/busuk'),
          _buildChecklistItem('Warna makanan normal'),
          _buildChecklistItem('Tekstur makanan sesuai'),
          _buildChecklistItem('Suhu makanan masih hangat'),
          _buildChecklistItem('Kebersihan dan higienitas terjaga'),
          _buildChecklistItem('Porsi sesuai dengan standar'),
          const SizedBox(height: AppSpacing.lg),
          AppCheckboxField(
            value: _testFoodApproved,
            onChanged: (value) {
              setState(() {
                _testFoodApproved = value ?? false;
              });
            },
            label: 'Test Food Approved',
            description: 'Saya menyatakan bahwa makanan telah lulus quality control dan siap untuk didistribusikan',
          ),
        ],
      ),
    );
  }

  Widget _buildChecklistItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.xs),
      child: Row(
        children: [        Icon(
          AppIcons.checkmark,
          size: 20,
          color: AppColors.primary,
        ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              text,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return AppCardFactory.basic(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Catatan QC',
            style: AppTypography.h6,
          ),
          const SizedBox(height: AppSpacing.sm),
          AppFormField(
            controller: _notesController,
            label: 'Catatan',
            hint: 'Tambahkan catatan khusus tentang kondisi makanan, jika ada...',
            maxLines: 4,
            keyboardType: TextInputType.multiline,
            prefixIcon: AppIcons.edit,
            validator: (value) {
              // Catatan adalah optional
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: AppButtonFactory.primary(
            text: _isLoading ? 'Menyimpan...' : 'Setujui & Lanjutkan Distribusi',
            onPressed: _testFoodApproved ? _submitQc : null,
            isLoading: _isLoading,
            icon: AppIcons.checkmark,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        SizedBox(
          width: double.infinity,
          child: AppButtonFactory.secondary(
            text: 'Tolak & Hentikan Distribusi',
            onPressed: _isLoading ? null : _rejectQc,
            icon: AppIcons.cancel,
          ),
        ),
      ],
    );
  }

  Future<void> _submitQc() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      _logger.i('Submitting QC approval for menu: ${_todayMenu['menuUtama']}');
      
      // Simulasi proses saving
      await Future.delayed(const Duration(seconds: 2));
      
      // In real app, save to database:
      // - log_qc table with is_approved = true
      // - update production status to ready for distribution
      // - notify logistics team
      
      _logger.i('QC approved successfully');
      
      if (mounted) {
        AppNotifications.showSnackBar(
          context,
          message: 'QC approved! Makanan siap untuk didistribusikan.',
          type: NotificationType.success,
        );
        
        // Navigate back to dashboard
        Navigator.pop(context);
      }
    } catch (e) {
      _logger.e('Failed to submit QC: $e');
      
      if (mounted) {
        AppNotifications.showSnackBar(
          context,
          message: 'Gagal menyimpan QC. Silakan coba lagi.',
          type: NotificationType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _rejectQc() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tolak Test Food'),
        content: const Text(
          'Apakah Anda yakin ingin menolak test food ini? '
          'Distribusi akan dihentikan dan produksi harus diulangi.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.errorRed,
            ),
            child: const Text('Tolak'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        _logger.i('Rejecting QC for menu: ${_todayMenu['menuUtama']}');
        
        // Simulasi proses saving
        await Future.delayed(const Duration(seconds: 2));
        
        // In real app, save to database:
        // - log_qc table with is_approved = false
        // - update production status to rejected
        // - notify kitchen team for re-production
        
        _logger.i('QC rejected successfully');
        
        if (mounted) {
          AppNotifications.showSnackBar(
            context,
            message: 'QC rejected. Produksi harus diulangi.',
            type: NotificationType.warning,
          );
          
          // Navigate back to dashboard
          Navigator.pop(context);
        }
      } catch (e) {
        _logger.e('Failed to reject QC: $e');
        
        if (mounted) {
          AppNotifications.showSnackBar(
            context,
            message: 'Gagal menolak QC. Silakan coba lagi.',
            type: NotificationType.error,
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
