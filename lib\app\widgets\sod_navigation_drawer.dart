import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';
import '../constants/app_radius.dart' as radius;
import '../config/app_router.dart';
import 'app_button.dart';

/// Navigation Drawer khusus untuk SOD-MBG
/// Menampilkan menu navigasi berdasarkan role user dengan design YellowBlueSkyHappy
class SodNavigationDrawer extends StatefulWidget {
  final String? userName;
  final String? userRole;
  final String? sppgName;
  final String? userAvatarUrl;
  final VoidCallback? onProfileTap;
  final VoidCallback? onSettingsTap;
  final VoidCallback? onLogoutTap;
  final int? selectedIndex;
  final ValueChanged<int>? onItemSelected;

  const SodNavigationDrawer({
    super.key,
    this.userName,
    this.userRole,
    this.sppgName,
    this.userAvatarUrl,
    this.onProfileTap,
    this.onSettingsTap,
    this.onLogoutTap,
    this.selectedIndex,
    this.onItemSelected,
  });

  @override
  State<SodNavigationDrawer> createState() => _SodNavigationDrawerState();
}

class _SodNavigationDrawerState extends State<SodNavigationDrawer> {
  final Logger _logger = Logger();
  late List<SodDrawerItem> _menuItems;
  
  @override
  void initState() {
    super.initState();
    _menuItems = _buildMenuItems();
  }

  List<SodDrawerItem> _buildMenuItems() {
    // Menu berdasarkan role - dapat dikustomisasi
    final role = widget.userRole?.toLowerCase() ?? '';
    
    List<SodDrawerItem> items = [
      // Header Section
      SodDrawerItem.header('Menu Utama'),
      
      // Dashboard - selalu ada
      SodDrawerItem(
        icon: AppIcons.dashboard,
        title: 'Dashboard',
        subtitle: 'Overview operasional',
        onTap: () => _navigateToRoute(AppRouter.dashboard, 0),
        isSelected: widget.selectedIndex == 0,
      ),
    ];

    // Kitchen Management - untuk operational roles
    if (['kepala_dapur', 'ahli_gizi', 'admin_yayasan', 'perwakilan_yayasan'].contains(role)) {
      items.add(
        SodDrawerItem(
          icon: AppIcons.kitchen,
          title: 'Kitchen Management',
          subtitle: 'Operasional dapur',
          onTap: () => _navigateToRoute(AppRouter.kitchenManagement, 1),
          isSelected: widget.selectedIndex == 1,
          trailingWidget: _buildNotificationBadge(3), // Contoh notifikasi
        ),
      );
    }

    // Inventory - untuk semua role kecuali pengawas
    if (role != 'pengawas_pemeliharaan') {
      items.add(
        SodDrawerItem(
          icon: AppIcons.inventory,
          title: 'Inventory',
          subtitle: 'Manajemen stok',
          onTap: () => _navigateToRoute(AppRouter.inventory, 2),
          isSelected: widget.selectedIndex == 2,
        ),
      );
    }

    // Logistics - untuk delivery dan admin
    if (['pengawas_pemeliharaan', 'admin_yayasan', 'perwakilan_yayasan', 'kepala_dapur'].contains(role)) {
      items.add(
        SodDrawerItem(
          icon: AppIcons.delivery,
          title: 'Logistics',
          subtitle: 'Distribusi & pengiriman',
          onTap: () => _navigateToRoute(AppRouter.logistics, 3),
          isSelected: widget.selectedIndex == 3,
        ),
      );
    }

    // Financial - untuk akuntan dan admin
    if (['akuntan', 'admin_yayasan', 'perwakilan_yayasan'].contains(role)) {
      items.add(
        SodDrawerItem(
          icon: AppIcons.money,
          title: 'Financial',
          subtitle: 'Keuangan & budget',
          onTap: () => _navigateToRoute(AppRouter.financial, 4),
          isSelected: widget.selectedIndex == 4,
        ),
      );
    }

    // Reports - untuk semua role
    items.addAll([
      SodDrawerItem(
        icon: AppIcons.report,
        title: 'Reports',
        subtitle: 'Laporan operasional',
        onTap: () => _navigateToRoute(AppRouter.reporting, 5),
        isSelected: widget.selectedIndex == 5,
      ),
      
      // Divider
      SodDrawerItem.divider(),
      
      // Quick Actions Section
      SodDrawerItem.header('Quick Actions'),
      
      SodDrawerItem(
        icon: AppIcons.qualityControl,
        title: 'QC Cepat',
        subtitle: 'Quality control harian',
        onTap: () => _performQuickQc(),
        iconColor: AppColors.successGreen,
      ),
      
      SodDrawerItem(
        icon: AppIcons.stockIn,
        title: 'Input Stok',
        subtitle: 'Catat penerimaan bahan',
        onTap: () => _performQuickStockInput(),
        iconColor: AppColors.primary,
      ),
      
      SodDrawerItem(
        icon: AppIcons.sync,
        title: 'Sync Data',
        subtitle: 'Sinkronisasi offline',
        onTap: () => _performDataSync(),
        iconColor: AppColors.infoBlue,
        trailingWidget: _buildSyncStatus(),
      ),
    ]);

    return items;
  }

  Widget _buildNotificationBadge(int count) {
    if (count == 0) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: AppColors.errorRed,
        borderRadius: BorderRadius.circular(radius.AppRadius.sm),
      ),
      constraints: const BoxConstraints(
        minWidth: 16,
        minHeight: 16,
      ),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: AppTypography.labelSmall.copyWith(
          color: AppColors.textOnPrimary,
          fontSize: 10,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildSyncStatus() {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: AppColors.successGreen,
        borderRadius: BorderRadius.circular(radius.AppRadius.sm),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.backgroundPrimary,
      child: Column(
        children: [
          _buildDrawerHeader(),
          Expanded(
            child: _buildDrawerBody(),
          ),
          _buildDrawerFooter(),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.primary,
            AppColors.primaryDark,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // App Logo and Title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: AppColors.secondary,
                      borderRadius: BorderRadius.circular(radius.AppRadius.md),
                    ),
                    child: Icon(
                      AppIcons.restaurant,
                      color: AppColors.primary,
                      size: AppIcons.sizeLarge,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'SOD-MBG',
                          style: AppTypography.h5.copyWith(
                            color: AppColors.textOnPrimary,
                            fontWeight: AppTypography.bold,
                          ),
                        ),
                        Text(
                          'Sistem Operasional Dapur',
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.md),
              
              // User Info
              InkWell(
                onTap: widget.onProfileTap,
                borderRadius: BorderRadius.circular(radius.AppRadius.md),
                child: Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: AppColors.textOnPrimary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(radius.AppRadius.md),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: AppColors.secondary,
                        backgroundImage: widget.userAvatarUrl != null
                            ? NetworkImage(widget.userAvatarUrl!)
                            : null,
                        child: widget.userAvatarUrl == null
                            ? Icon(
                                _getRoleIcon(widget.userRole),
                                color: AppColors.primary,
                                size: AppIcons.sizeMedium,
                              )
                            : null,
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.userName ?? 'User',
                              style: AppTypography.labelLarge.copyWith(
                                color: AppColors.textOnPrimary,
                                fontWeight: AppTypography.semiBold,
                              ),
                            ),
                            Text(
                              _getRoleDisplayName(widget.userRole),
                              style: AppTypography.labelSmall.copyWith(
                                color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                              ),
                            ),
                            if (widget.sppgName != null)
                              Text(
                                widget.sppgName!,
                                style: AppTypography.labelSmall.copyWith(
                                  color: AppColors.textOnPrimary.withValues(alpha: 0.6),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerBody() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      itemCount: _menuItems.length,
      itemBuilder: (context, index) {
        final item = _menuItems[index];
        return _buildDrawerItem(item);
      },
    );
  }

  Widget _buildDrawerItem(SodDrawerItem item) {
    if (item.isDivider) {
      return Container(
        margin: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        child: Divider(
          color: AppColors.neutralGray200,
          height: 1,
        ),
      );
    }

    if (item.isHeader) {
      return Container(
        padding: const EdgeInsets.fromLTRB(
          AppSpacing.md,
          AppSpacing.lg,
          AppSpacing.md,
          AppSpacing.sm,
        ),
        child: Text(
          item.title,
          style: AppTypography.labelMedium.copyWith(
            color: AppColors.textTertiary,
            fontWeight: AppTypography.semiBold,
            letterSpacing: 0.5,
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(AppSpacing.xs),
          decoration: BoxDecoration(
            color: item.isSelected
                ? AppColors.primary.withValues(alpha: 0.1)
                : AppColors.neutralGray100,
            borderRadius: BorderRadius.circular(radius.AppRadius.sm),
          ),
          child: Icon(
            item.icon,
            color: item.isSelected
                ? AppColors.primary
                : (item.iconColor ?? AppColors.textTertiary),
            size: AppIcons.sizeMedium,
          ),
        ),
        title: Text(
          item.title,
          style: AppTypography.labelLarge.copyWith(
            color: item.isSelected
                ? AppColors.primary
                : AppColors.textPrimary,
            fontWeight: item.isSelected
                ? AppTypography.semiBold
                : AppTypography.medium,
          ),
        ),
        subtitle: item.subtitle != null
            ? Text(
                item.subtitle!,
                style: AppTypography.labelSmall.copyWith(
                  color: AppColors.textTertiary,
                ),
              )
            : null,
        trailing: item.trailingWidget,
        selected: item.isSelected,
        selectedTileColor: AppColors.primary.withValues(alpha: 0.05),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.md),
        ),
        onTap: item.onTap,
      ),
    );
  }

  Widget _buildDrawerFooter() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.backgroundSecondary,
        border: Border(
          top: BorderSide(
            color: AppColors.neutralGray200,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: AppButtonFactory.outline(
                    text: 'Settings',
                    onPressed: widget.onSettingsTap ?? () => _navigateToSettings(),
                    icon: AppIcons.settings,
                    size: AppButtonSize.small,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: AppButtonFactory.text(
                    text: 'Logout',
                    onPressed: widget.onLogoutTap ?? () => _performLogout(),
                    icon: AppIcons.logout,
                    size: AppButtonSize.small,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Version 1.0.0 • Build 2025.01',
              style: AppTypography.labelSmall.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  IconData _getRoleIcon(String? role) {
    switch (role?.toLowerCase()) {
      case 'admin_yayasan':
        return AppIcons.adminYayasan;
      case 'perwakilan_yayasan':
        return AppIcons.perwakilanYayasan;
      case 'kepala_dapur':
        return AppIcons.kepalaDapur;
      case 'ahli_gizi':
        return AppIcons.ahliGizi;
      case 'akuntan':
        return AppIcons.akuntan;
      case 'pengawas_pemeliharaan':
        return AppIcons.pengawasPemeliharaan;
      default:
        return AppIcons.profile;
    }
  }

  String _getRoleDisplayName(String? role) {
    switch (role?.toLowerCase()) {
      case 'admin_yayasan':
        return 'Admin Yayasan';
      case 'perwakilan_yayasan':
        return 'Perwakilan Yayasan';
      case 'kepala_dapur':
        return 'Kepala Dapur SPPG';
      case 'ahli_gizi':
        return 'Ahli Gizi';
      case 'akuntan':
        return 'Akuntan';
      case 'pengawas_pemeliharaan':
        return 'Pengawas Pemeliharaan';
      default:
        return 'User';
    }
  }

  void _navigateToRoute(String route, int index) {
    _logger.d('Navigating to $route (index: $index)');
    Navigator.pop(context); // Close drawer
    widget.onItemSelected?.call(index);
    
    // Navigate berdasarkan route
    switch (route) {
      case AppRouter.dashboard:
        AppRouter.goToDashboard(context);
        break;
      case AppRouter.kitchenManagement:
        AppRouter.goToKitchenManagement(context);
        break;
      case AppRouter.inventory:
        AppRouter.goToInventory(context);
        break;
      case AppRouter.logistics:
        AppRouter.goToLogistics(context);
        break;
      case AppRouter.financial:
        AppRouter.goToFinancial(context);
        break;
      case AppRouter.reporting:
        AppRouter.goToReporting(context);
        break;
      default:
        AppRouter.goToDashboard(context);
    }
  }

  void _navigateToSettings() {
    _logger.d('Navigating to settings');
    Navigator.pop(context);
    AppRouter.goToUserManagement(context);
  }

  void _performLogout() {
    _logger.d('Performing logout');
    Navigator.pop(context);
    AppRouter.goToLogin(context);
  }

  void _performQuickQc() {
    _logger.d('Performing quick QC');
    Navigator.pop(context);
    // TODO: Implement quick QC
  }

  void _performQuickStockInput() {
    _logger.d('Performing quick stock input');
    Navigator.pop(context);
    // TODO: Implement quick stock input
  }

  void _performDataSync() {
    _logger.d('Performing data sync');
    Navigator.pop(context);
    // TODO: Implement data sync
  }
}

/// Model untuk item di drawer
class SodDrawerItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool isDivider;
  final bool isHeader;
  final Widget? trailingWidget;
  final Color? iconColor;

  const SodDrawerItem({
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.isSelected = false,
    this.isDivider = false,
    this.isHeader = false,
    this.trailingWidget,
    this.iconColor,
  });

  /// Factory untuk membuat divider
  const SodDrawerItem.divider()
      : icon = Icons.clear,
        title = '',
        subtitle = null,
        onTap = null,
        isSelected = false,
        isDivider = true,
        isHeader = false,
        trailingWidget = null,
        iconColor = null;

  /// Factory untuk membuat header
  const SodDrawerItem.header(String title)
      : icon = Icons.clear,
        title = title,
        subtitle = null,
        onTap = null,
        isSelected = false,
        isDivider = false,
        isHeader = true,
        trailingWidget = null,
        iconColor = null;
}
