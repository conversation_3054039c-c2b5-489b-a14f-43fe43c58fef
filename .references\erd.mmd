erDiagram
    yayasan {
        UUID id PK "Primary Key"
        TEXT nama "<PERSON>a <PERSON>"
        TEXT npwp "NPWP Yayasan"
        TEXT alamat "Alamat Yayasan"
    }

    sppg {
        UUID id PK "Primary Key"
        TEXT nama "Nama Dapur SPPG"
        TEXT alamat "Alamat Dapur SPPG"
        BOOLEAN is_mitra_non_yayasan "Flag untuk mitra"
        UUID yayasan_id FK "Relasi ke Yayasan"
    }

    users {
        UUID id PK "User ID dari Supabase Auth"
        TEXT nama "Nama Lengkap Pengguna"
        EMAIL email "Email untuk login"
        UUID role_id FK "Relasi ke peran pengguna"
        UUID sppg_id FK "SPPG tempat pengguna bertugas (nullable untuk admin)"
    }

    roles {
        UUID id PK "Primary Key"
        TEXT nama_peran "Contoh: admin_yayasan, kepala_sppg"
        TEXT deskripsi "Deskripsi hak akses peran"
    }

    penerima_manfaat {
        UUID id PK "Primary Key"
        TEXT nama "Nama Sekolah/Posyandu"
        TEXT alamat "Alamat Penerima"
        INT jumlah_siswa "Estimasi jumlah penerima di lokasi ini"
        UUID sppg_id FK "Relasi ke SPPG yang melayani"
    }

    menu {
        UUID id PK "Primary Key"
        TEXT nama_menu "Contoh: Nasi, Ayam Goreng, Tumis Buncis"
        TEXT deskripsi "Deskripsi atau resep singkat"
        INT kalori
        INT protein
        INT lemak
        INT karbohidrat
        UUID created_by_user_id FK "Ahli Gizi yang membuat menu"
    }

    jadwal_menu {
        UUID id PK "Primary Key"
        DATE tanggal_jadwal "Tanggal menu ini akan disajikan"
        UUID menu_id FK "Menu yang dijadwalkan"
        UUID sppg_id FK "SPPG yang akan memasak menu ini"
    }

    log_produksi {
        UUID id PK "Primary Key"
        INT jumlah_porsi_produksi "Jumlah porsi yang dimasak"
        TIMESTAMP waktu_mulai_masak
        TIMESTAMP waktu_selesai_plating
        TEXT catatan_produksi "Catatan dari Kepala SPPG"
        UUID jadwal_menu_id FK "Relasi ke jadwal menu harian"
    }

    log_qc {
        UUID id PK "Primary Key"
        BOOLEAN is_approved "Status persetujuan test food"
        TEXT catatan_qc "Catatan dari Kepala SPPG"
        TIMESTAMP waktu_qc "Waktu pengecekan"
        UUID log_produksi_id FK "Produksi yang di-QC"
        UUID user_id FK "Kepala SPPG/Ahli Gizi yang melakukan QC"
    }

    log_distribusi {
        UUID id PK "Primary Key"
        INT jumlah_porsi_terkirim
        TIMESTAMP waktu_terkirim "Waktu saat driver mengubah status"
        TEXT status "Dikirim, Diterima, Gagal"
        TEXT foto_bukti_url "URL foto serah terima di Supabase Storage"
        TEXT gps_koordinat "Koordinat GPS saat serah terima"
        UUID log_produksi_id FK "Batch produksi yang didistribusikan"
        UUID penerima_manfaat_id FK "Lokasi tujuan"
        UUID driver_id FK "User Pengawas/Driver yang mengirim"
    }

    transaksi_keuangan {
        UUID id PK "Primary Key"
        TEXT tipe "Pemasukan, Pengeluaran"
        DECIMAL jumlah "Nilai transaksi"
        TEXT deskripsi "Contoh: Pembelian beras 200kg"
        TEXT bukti_url "URL foto nota/kuitansi"
        TIMESTAMP tanggal_transaksi
        UUID sppg_id FK "Relasi ke SPPG terkait"
        UUID user_id FK "Akuntan yang mencatat"
    }

    inventory_items {
        UUID id PK "Primary Key"
        TEXT nama_item "Contoh: Beras, Minyak Goreng, Ayam"
        TEXT satuan "kg, liter, ekor"
    }

    sppg_inventory {
        UUID sppg_id FK
        UUID item_id FK
        INT kuantitas "Jumlah stok saat ini"
        TEXT primary_key "sppg_id, item_id"
    }

    log_inventory {
        UUID id PK "Primary Key"
        TEXT tipe "MASUK, KELUAR"
        INT kuantitas "Jumlah item yang masuk/keluar"
        TIMESTAMP waktu_pencatatan
        UUID sppg_id FK
        UUID item_id FK
        UUID user_id FK "User yang mencatat (Kepala Gudang/SPPG)"
    }

    yayasan ||--o{ sppg : "memiliki"
    sppg ||--o{ users : "mempekerjakan"
    roles ||--o{ users : "diberikan_kepada"
    sppg ||--o{ penerima_manfaat : "melayani"
    sppg ||--o{ jadwal_menu : "memiliki_jadwal"
    menu ||--o{ jadwal_menu : "dijadwalkan_pada"
    jadwal_menu ||--|{ log_produksi : "menghasilkan"
    log_produksi ||--|{ log_qc : "diperiksa_oleh"
    log_produksi ||--o{ log_distribusi : "didistribusikan"
    penerima_manfaat ||--o{ log_distribusi : "menerima"
    sppg ||--o{ transaksi_keuangan : "memiliki_transaksi"
    sppg }o--o{ inventory_items : "menyimpan"
    sppg_inventory ||--o{ log_inventory : "memiliki_histori"