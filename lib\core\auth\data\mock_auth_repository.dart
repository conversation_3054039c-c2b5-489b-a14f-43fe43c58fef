import 'dart:async';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../domain/auth_repository.dart';
import '../domain/app_user.dart';
import '../domain/auth_state.dart';

/// Mock implementation of AuthRepository for development
/// Used when Supabase is not configured
class MockAuthRepository implements AuthRepository {
  final Logger _logger = Logger();
  
  // Stream controllers
  final StreamController<AuthState> _authStateController = StreamController<AuthState>.broadcast();
  
  // Current state
  AuthState _currentAuthState = const AuthInitialState();
  
  // Mock users storage
  final Map<String, Map<String, String>> _mockUsers = {};
  AppUser? _currentUser;
  
  // SharedPreferences
  SharedPreferences? _sharedPreferences;
  
  @override
  Stream<AuthState> get authStateStream => _authStateController.stream;
  
  @override
  AuthState get currentAuthState => _currentAuthState;
  
  @override
  AppUser? get currentUser => _currentUser;
  
  /// Initialize mock repository
  Future<void> initialize() async {
    _logger.i('Initializing MockAuthRepository');
    
    try {
      _sharedPreferences = await SharedPreferences.getInstance();
      
      // Load cached user if exists
      final cachedUser = await getCachedUserData();
      if (cachedUser != null) {
        _currentUser = cachedUser;
        _emitState(AuthenticatedState(user: cachedUser));
      } else {
        _emitState(const UnauthenticatedState());
      }
      
      _logger.i('MockAuthRepository initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize MockAuthRepository: $e');
      _emitState(const UnauthenticatedState());
    }
  }
  
  /// Emit new auth state
  void _emitState(AuthState state) {
    _currentAuthState = state;
    _authStateController.add(state);
    _logger.d('Auth state emitted: ${state.runtimeType}');
  }
  
  @override
  Future<AuthState> signInWithEmail({
    required String email,
    required String password,
  }) async {
    _logger.i('Mock sign in with email: $email');
    
    try {
      _emitState(const AuthLoadingState(message: 'Signing in...'));
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Check if user exists
      if (_mockUsers.containsKey(email)) {
        final userData = _mockUsers[email]!;
        if (userData['password'] == password) {
          // Sign in successful
          final user = AppUser(
            id: 'mock_${email.replaceAll('@', '_').replaceAll('.', '_')}',
            email: email,
            nama: userData['nama'] ?? 'Mock User',
            role: userData['role'] ?? 'kepala_dapur',
            sppgName: userData['sppgName'],
            isAnonymous: false,
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
          );
          
          _currentUser = user;
          await cacheUserData(user);
          
          final state = AuthenticatedState(user: user);
          _emitState(state);
          return state;
        }
      }
      
      // Sign in failed
      final errorState = AuthErrorState(error: 'Invalid email or password');
      _emitState(errorState);
      return errorState;
      
    } catch (e) {
      _logger.e('Mock sign in failed: $e');
      final errorState = AuthErrorState(error: 'Sign in failed: $e');
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
    String? sppgId,
    String? sppgName,
  }) async {
    _logger.i('Mock sign up with email: $email');
    
    try {
      _emitState(const AuthLoadingState(message: 'Creating account...'));
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));
      
      // Check if user already exists
      if (_mockUsers.containsKey(email)) {
        final errorState = AuthErrorState(error: 'User already exists');
        _emitState(errorState);
        return errorState;
      }
      
      // Create mock user
      _mockUsers[email] = {
        'password': password,
        'nama': nama,
        'role': role,
        'sppgId': sppgId ?? '',
        'sppgName': sppgName ?? '',
      };
      
      // Create user object
      final user = AppUser(
        id: 'mock_${email.replaceAll('@', '_').replaceAll('.', '_')}',
        email: email,
        nama: nama,
        role: role,
        sppgId: sppgId,
        sppgName: sppgName,
        isAnonymous: false,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );
      
      _currentUser = user;
      await cacheUserData(user);
      
      final state = AuthenticatedState(user: user, isFirstLogin: true);
      _emitState(state);
      return state;
      
    } catch (e) {
      _logger.e('Mock sign up failed: $e');
      final errorState = AuthErrorState(error: 'Sign up failed: $e');
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> signInAnonymously() async {
    _logger.i('Mock anonymous sign in');
    
    try {
      _emitState(const AuthLoadingState(message: 'Signing in as guest...'));
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      final user = AppUser(
        id: 'mock_anonymous_${DateTime.now().millisecondsSinceEpoch}',
        email: null,
        nama: 'Guest User',
        role: 'guest',
        isAnonymous: true,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );
      
      _currentUser = user;
      await cacheUserData(user);
      
      final state = AnonymousState(user: user);
      _emitState(state);
      return state;
      
    } catch (e) {
      _logger.e('Mock anonymous sign in failed: $e');
      final errorState = AuthErrorState(error: 'Anonymous sign in failed: $e');
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> signOut() async {
    _logger.i('Mock sign out');
    
    try {
      _emitState(const LoggingOutState());
      
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      _currentUser = null;
      await clearCache();
      
      final state = const UnauthenticatedState();
      _emitState(state);
      return state;
      
    } catch (e) {
      _logger.e('Mock sign out failed: $e');
      final errorState = AuthErrorState(error: 'Sign out failed: $e');
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> refreshToken() async {
    _logger.d('Mock refresh token');
    
    if (_currentUser != null) {
      final updatedUser = _currentUser!.copyWith(
        lastLoginAt: DateTime.now(),
      );
      _currentUser = updatedUser;
      await cacheUserData(updatedUser);
    }
    
    return _currentAuthState;
  }
  
  @override
  Future<bool> resetPassword({required String email}) async {
    _logger.i('Mock reset password for: $email');
    
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Always return true for mock
    return true;
  }
  
  @override
  Future<bool> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _logger.i('Mock update password');
    
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Always return true for mock
    return true;
  }
  
  @override
  Future<AuthState> updateProfile({
    String? nama,
    String? email,
    Map<String, dynamic>? metadata,
  }) async {
    _logger.i('Mock update profile');
    
    try {
      _emitState(const AuthLoadingState(message: 'Updating profile...'));
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      if (_currentUser != null) {
        final updatedUser = _currentUser!.copyWith(
          nama: nama ?? _currentUser!.nama,
          email: email ?? _currentUser!.email,
          metadata: metadata ?? _currentUser!.metadata,
        );
        
        _currentUser = updatedUser;
        await cacheUserData(updatedUser);
        
        final state = AuthenticatedState(user: updatedUser);
        _emitState(state);
        return state;
      }
      
      final errorState = AuthErrorState(error: 'No user to update');
      _emitState(errorState);
      return errorState;
      
    } catch (e) {
      _logger.e('Mock update profile failed: $e');
      final errorState = AuthErrorState(error: 'Update profile failed: $e');
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AppUser?> getUserProfile(String userId) async {
    return _currentUser?.id == userId ? _currentUser : null;
  }
  
  @override
  Future<bool> saveUserProfile(AppUser user) async {
    _currentUser = user;
    await cacheUserData(user);
    return true;
  }
  
  @override
  Future<bool> isSessionValid() async {
    return _currentUser != null;
  }
  
  @override
  Future<Map<String, dynamic>?> getSessionInfo() async {
    if (_currentUser == null) return null;
    
    return {
      'user_id': _currentUser!.id,
      'email': _currentUser!.email,
      'role': _currentUser!.role,
      'mock': true,
    };
  }
  
  @override
  Future<bool> extendAnonymousSession() async {
    return _currentUser?.isAnonymous == true;
  }
  
  @override
  Future<void> cacheUserData(AppUser user) async {
    try {
      await _sharedPreferences?.setString('mock_cached_user', user.toJson().toString());
    } catch (e) {
      _logger.e('Failed to cache user data: $e');
    }
  }
  
  @override
  Future<AppUser?> getCachedUserData() async {
    try {
      final userData = _sharedPreferences?.getString('mock_cached_user');
      if (userData != null) {
        // This is a simplified approach - in real implementation you'd use proper JSON parsing
        return null; // For now, return null to avoid parsing issues
      }
      return null;
    } catch (e) {
      _logger.e('Failed to get cached user data: $e');
      return null;
    }
  }
  
  @override
  Future<void> clearCache() async {
    try {
      await _sharedPreferences?.remove('mock_cached_user');
    } catch (e) {
      _logger.e('Failed to clear cache: $e');
    }
  }
  
  @override
  bool isEmailValid(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }
  
  @override
  bool isPasswordValid(String password) {
    return password.length >= 6;
  }
  
  @override
  Map<String, dynamic> getPasswordRequirements() {
    return {
      'minLength': 6,
      'requireLetter': false,
      'requireNumber': false,
      'requireSpecialChar': false,
      'requireUppercase': false,
    };
  }
  
  @override
  void dispose() {
    _logger.d('Disposing MockAuthRepository');
    _authStateController.close();
  }
}
