import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/widgets/app_button.dart';
import '../../../../app/config/app_router.dart';

/// Halaman login untuk Aplikasi SOD-MBG
/// Menampilkan form login dengan design system yang konsisten
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  String _selectedRole = 'kepala_dapur';

  final List<Map<String, dynamic>> _roles = [
    {
      'value': 'admin_yayasan',
      'label': 'Admin Yayasan',
      'icon': AppIcons.adminYayasan,
      'color': AppColors.adminYayasanColor,
    },
    {
      'value': 'perwakilan_yayasan',
      'label': 'Perwakilan Yayasan',
      'icon': AppIcons.perwakilanYayasan,
      'color': AppColors.perwakilanYayasanColor,
    },
    {
      'value': 'kepala_dapur',
      'label': 'Kepala Dapur SPPG',
      'icon': AppIcons.kepalaDapur,
      'color': AppColors.kepalaDapurColor,
    },
    {
      'value': 'ahli_gizi',
      'label': 'Ahli Gizi',
      'icon': AppIcons.ahliGizi,
      'color': AppColors.ahliGiziColor,
    },
    {
      'value': 'akuntan',
      'label': 'Akuntan',
      'icon': AppIcons.akuntan,
      'color': AppColors.akuntanColor,
    },
    {
      'value': 'pengawas_pemeliharaan',
      'label': 'Pengawas Pemeliharaan & Penghantaran',
      'icon': AppIcons.pengawasPemeliharaan,
      'color': AppColors.pengawasPemeliharaanColor,
    },
  ];

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building LoginPage');
    
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1200;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: _buildBackgroundDecoration(),
        child: SafeArea(
          child: isDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryLight,
          AppColors.primary,
          AppColors.primaryDark,
        ],
        stops: [0.0, 0.6, 1.0],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Welcome illustration
        Expanded(
          flex: 3,
          child: _buildWelcomeSection(),
        ),
        // Right side - Login form
        Expanded(
          flex: 2,
          child: Container(
            decoration: const BoxDecoration(
              color: AppColors.backgroundPrimary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                bottomLeft: Radius.circular(32),
              ),
            ),
            child: _buildLoginSection(),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.xl),
          _buildMobileHeader(),
          const SizedBox(height: AppSpacing.xl),
          _buildLoginCard(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo and branding
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppColors.secondary,
                  borderRadius: BorderRadius.circular(radius.AppRadius.lg),
                ),
                child: const Icon(
                  AppIcons.restaurant,
                  size: 48,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h1.copyWith(
                      color: AppColors.textOnPrimary,
                      fontWeight: AppTypography.bold,
                    ),
                  ),
                  Text(
                    'Sistem Operasional Dapur',
                    style: AppTypography.h6.copyWith(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Welcome message
          Text(
            'Selamat Datang!',
            style: AppTypography.h2.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: AppTypography.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Kelola operasional dapur dengan mudah dan efisien. '
            'Sistem terintegrasi untuk Makan Bergizi Gratis.',
            style: AppTypography.bodyLarge.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Feature highlights
          _buildFeatureHighlights(),
        ],
      ),
    );
  }

  Widget _buildFeatureHighlights() {
    final features = [
      {
        'icon': AppIcons.dashboard,
        'title': 'Dashboard Terpadu',
        'description': 'Monitoring real-time operasional dapur',
      },
      {
        'icon': AppIcons.qualityControl,
        'title': 'Quality Control',
        'description': 'Jaminan kualitas makanan bergizi',
      },
      {
        'icon': AppIcons.offline,
        'title': 'Mode Offline',
        'description': 'Tetap bisa beroperasi tanpa internet',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) {
        return Container(
          margin: const EdgeInsets.only(bottom: AppSpacing.md),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(radius.AppRadius.md),
                ),
                child: Icon(
                  feature['icon'] as IconData,
                  color: AppColors.secondary,
                  size: AppIcons.sizeMedium,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feature['title'] as String,
                      style: AppTypography.labelLarge.copyWith(
                        color: AppColors.textOnPrimary,
                        fontWeight: AppTypography.semiBold,
                      ),
                    ),
                    Text(
                      feature['description'] as String,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textOnPrimary.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMobileHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColors.secondary,
            borderRadius: BorderRadius.circular(radius.AppRadius.xl),
          ),
          child: const Icon(
            AppIcons.restaurant,
            size: 64,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        Text(
          'SOD-MBG',
          style: AppTypography.h2.copyWith(
            color: AppColors.textOnPrimary,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Sistem Operasional Dapur',
          style: AppTypography.h6.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.8),
          ),
        ),
        Text(
          'Makan Bergizi Gratis',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Masuk ke Akun',
            style: AppTypography.h4.copyWith(
              color: AppColors.textPrimary,
              fontWeight: AppTypography.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Masukkan kredensial Anda untuk mengakses sistem',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.xl),
          _buildLoginForm(),
        ],
      ),
    );
  }

  Widget _buildLoginCard() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(radius.AppRadius.xl),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: _buildLoginForm(),
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildRoleSelector(),
          const SizedBox(height: AppSpacing.lg),
          _buildUsernameField(),
          const SizedBox(height: AppSpacing.md),
          _buildPasswordField(),
          const SizedBox(height: AppSpacing.lg),
          _buildLoginButton(),
          const SizedBox(height: AppSpacing.md),
          _buildForgotPasswordButton(),
          const SizedBox(height: AppSpacing.lg),
          _buildOfflineIndicator(),
        ],
      ),
    );
  }

  Widget _buildRoleSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pilih Role',
          style: AppTypography.labelLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: AppTypography.medium,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.neutralGray300),
            borderRadius: BorderRadius.circular(AppRadius.input),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedRole,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              items: _roles.map((role) {
                return DropdownMenuItem<String>(
                  value: role['value'],
                  child: Row(
                    children: [
                      Icon(
                        role['icon'],
                        color: role['color'],
                        size: AppIcons.sizeMedium,
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Text(
                        role['label'],
                        style: AppTypography.bodyMedium,
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedRole = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUsernameField() {
    return TextFormField(
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: 'Username/Email',
        hintText: 'Masukkan username atau email',
        prefixIcon: const Icon(AppIcons.user),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
        ),
      ),
      style: AppTypography.inputText,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Username/Email harus diisi';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: 'Password',
        hintText: 'Masukkan password',
        prefixIcon: const Icon(AppIcons.security),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? AppIcons.visibilityOff : AppIcons.visibility,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.input),
        ),
      ),
      style: AppTypography.inputText,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password harus diisi';
        }
        if (value.length < 6) {
          return 'Password minimal 6 karakter';
        }
        return null;
      },
    );
  }

  Widget _buildLoginButton() {
    return AppButtonFactory.primary(
      text: 'Masuk',
      onPressed: _isLoading ? null : _handleLogin,
      icon: AppIcons.login,
      isLoading: _isLoading,
      isFullWidth: true,
      size: AppButtonSize.large,
    );
  }

  Widget _buildForgotPasswordButton() {
    return AppButtonFactory.text(
      text: 'Lupa Password?',
      onPressed: _handleForgotPassword,
      size: AppButtonSize.small,
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: AppColors.kitchenClean,
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            AppIcons.offline,
            size: AppIcons.sizeSmall,
            color: AppColors.successGreen,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            'Mode Offline Tersedia',
            style: AppTypography.labelSmall.copyWith(
              color: AppColors.successGreen,
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogin() async {
    _logger.d('Login attempt for role: $_selectedRole');
    
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate login process
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      // Navigate to dashboard
      AppRouter.goToDashboard(context);
    }
  }

  void _handleForgotPassword() {
    _logger.d('Forgot password clicked');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Password'),
        content: const Text(
          'Fitur reset password akan segera tersedia. '
          'Hubungi administrator untuk bantuan.',
        ),
        actions: [
          AppButtonFactory.text(
            text: 'OK',
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
