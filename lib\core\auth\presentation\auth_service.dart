import 'dart:async';
import 'package:logger/logger.dart';

import '../domain/auth_repository.dart';
import '../domain/app_user.dart';
import '../domain/auth_state.dart';
import '../data/supabase_auth_repository.dart';
import '../data/mock_auth_repository.dart';
import '../../config/supabase_service.dart';

/// Service untuk mengelola authentication
/// Bertindak sebagai facade untuk auth repository
class AuthService {
  static final Logger _logger = Logger();
  
  // Singleton pattern
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();
  
  // Auth repository
  late final AuthRepository _authRepository;
  
  // Initialization flag
  bool _isInitialized = false;
  
  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Check if service is using mock authentication
  bool get isMockMode => _authRepository is MockAuthRepository;
  
  /// Initialize auth service
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.d('AuthService already initialized');
      return;
    }
    
    try {
      _logger.i('Initializing AuthService');
      
      // Try to initialize Supabase service first
      final supabaseService = SupabaseService.instance;
      final supabaseInitialized = await supabaseService.initialize();
      
      if (supabaseInitialized) {
        _logger.i('Using Supabase authentication');
        // Initialize Supabase auth repository
        _authRepository = SupabaseAuthRepository();
        await (_authRepository as SupabaseAuthRepository).initialize();
      } else {
        _logger.w('Supabase not available, using mock authentication');
        // Initialize mock auth repository
        _authRepository = MockAuthRepository();
        await (_authRepository as MockAuthRepository).initialize();
      }
      
      _isInitialized = true;
      _logger.i('AuthService initialized successfully');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize AuthService: $e', stackTrace: stackTrace);
      
      // Fallback to mock repository
      _logger.w('Falling back to mock authentication');
      try {
        _authRepository = MockAuthRepository();
        await (_authRepository as MockAuthRepository).initialize();
        _isInitialized = true;
        _logger.i('AuthService initialized with mock repository');
      } catch (mockError) {
        _logger.e('Failed to initialize mock repository: $mockError');
        rethrow;
      }
    }
  }
  
  // ===== DELEGATION METHODS =====
  
  /// Stream untuk mendengarkan perubahan auth state
  Stream<AuthState> get authStateStream {
    _ensureInitialized();
    return _authRepository.authStateStream;
  }
  
  /// Get current auth state
  AuthState get currentAuthState {
    _ensureInitialized();
    return _authRepository.currentAuthState;
  }
  
  /// Get current user
  AppUser? get currentUser {
    _ensureInitialized();
    return _authRepository.currentUser;
  }
  
  /// Check if user is logged in
  bool get isLoggedIn {
    _ensureInitialized();
    return _authRepository.currentUser != null;
  }
  
  /// Check if user is anonymous
  bool get isAnonymous {
    _ensureInitialized();
    return _authRepository.currentUser?.isAnonymous ?? false;
  }
  
  // ===== AUTHENTICATION METHODS =====
  
  /// Login dengan email dan password
  Future<AuthState> signInWithEmail({
    required String email,
    required String password,
  }) async {
    _ensureInitialized();
    _logger.i('Signing in with email: $email');
    
    try {
      final result = await _authRepository.signInWithEmail(
        email: email,
        password: password,
      );
      
      _logger.i('Sign in completed with state: ${result.runtimeType}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Sign in failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Register dengan email dan password
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
    String? sppgId,
    String? sppgName,
  }) async {
    _ensureInitialized();
    _logger.i('Signing up with email: $email');
    
    try {
      final result = await _authRepository.signUpWithEmail(
        email: email,
        password: password,
        nama: nama,
        role: role,
        sppgId: sppgId,
        sppgName: sppgName,
      );
      
      _logger.i('Sign up completed with state: ${result.runtimeType}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Sign up failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Login sebagai anonymous
  Future<AuthState> signInAnonymously() async {
    _ensureInitialized();
    _logger.i('Signing in anonymously');
    
    try {
      final result = await _authRepository.signInAnonymously();
      
      _logger.i('Anonymous sign in completed with state: ${result.runtimeType}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Anonymous sign in failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Logout
  Future<AuthState> signOut() async {
    _ensureInitialized();
    _logger.i('Signing out');
    
    try {
      final result = await _authRepository.signOut();
      
      _logger.i('Sign out completed with state: ${result.runtimeType}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Sign out failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Refresh token
  Future<AuthState> refreshToken() async {
    _ensureInitialized();
    _logger.d('Refreshing token');
    
    try {
      final result = await _authRepository.refreshToken();
      
      _logger.d('Token refresh completed with state: ${result.runtimeType}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Token refresh failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  // ===== PASSWORD MANAGEMENT =====
  
  /// Reset password
  Future<bool> resetPassword({
    required String email,
  }) async {
    _ensureInitialized();
    _logger.i('Resetting password for email: $email');
    
    try {
      final result = await _authRepository.resetPassword(email: email);
      
      _logger.i('Password reset ${result ? 'successful' : 'failed'}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Password reset failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Update password
  Future<bool> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _ensureInitialized();
    _logger.i('Updating password');
    
    try {
      final result = await _authRepository.updatePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      
      _logger.i('Password update ${result ? 'successful' : 'failed'}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Password update failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  // ===== PROFILE MANAGEMENT =====
  
  /// Update user profile
  Future<AuthState> updateProfile({
    String? nama,
    String? email,
    Map<String, dynamic>? metadata,
  }) async {
    _ensureInitialized();
    _logger.i('Updating user profile');
    
    try {
      final result = await _authRepository.updateProfile(
        nama: nama,
        email: email,
        metadata: metadata,
      );
      
      _logger.i('Profile update completed with state: ${result.runtimeType}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Profile update failed: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Get user profile
  Future<AppUser?> getUserProfile(String userId) async {
    _ensureInitialized();
    _logger.d('Getting user profile for: $userId');
    
    try {
      final result = await _authRepository.getUserProfile(userId);
      
      _logger.d('User profile ${result != null ? 'found' : 'not found'}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get user profile: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Save user profile
  Future<bool> saveUserProfile(AppUser user) async {
    _ensureInitialized();
    _logger.d('Saving user profile for: ${user.id}');
    
    try {
      final result = await _authRepository.saveUserProfile(user);
      
      _logger.d('User profile save ${result ? 'successful' : 'failed'}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to save user profile: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  // ===== SESSION MANAGEMENT =====
  
  /// Check if session is valid
  Future<bool> isSessionValid() async {
    _ensureInitialized();
    
    try {
      final result = await _authRepository.isSessionValid();
      _logger.d('Session validity check: $result');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Session validity check failed: $e', stackTrace: stackTrace);
      return false;
    }
  }
  
  /// Get session info
  Future<Map<String, dynamic>?> getSessionInfo() async {
    _ensureInitialized();
    
    try {
      final result = await _authRepository.getSessionInfo();
      _logger.d('Session info retrieved: ${result != null}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get session info: $e', stackTrace: stackTrace);
      return null;
    }
  }
  
  /// Extend anonymous session
  Future<bool> extendAnonymousSession() async {
    _ensureInitialized();
    _logger.d('Extending anonymous session');
    
    try {
      final result = await _authRepository.extendAnonymousSession();
      _logger.d('Anonymous session extension ${result ? 'successful' : 'failed'}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to extend anonymous session: $e', stackTrace: stackTrace);
      return false;
    }
  }
  
  // ===== CACHE MANAGEMENT =====
  
  /// Cache user data
  Future<void> cacheUserData(AppUser user) async {
    _ensureInitialized();
    _logger.d('Caching user data for: ${user.id}');
    
    try {
      await _authRepository.cacheUserData(user);
      _logger.d('User data cached successfully');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to cache user data: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Get cached user data
  Future<AppUser?> getCachedUserData() async {
    _ensureInitialized();
    _logger.d('Getting cached user data');
    
    try {
      final result = await _authRepository.getCachedUserData();
      _logger.d('Cached user data ${result != null ? 'found' : 'not found'}');
      return result;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get cached user data: $e', stackTrace: stackTrace);
      return null;
    }
  }
  
  /// Clear cache
  Future<void> clearCache() async {
    _ensureInitialized();
    _logger.d('Clearing cache');
    
    try {
      await _authRepository.clearCache();
      _logger.d('Cache cleared successfully');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to clear cache: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
  
  // ===== VALIDATION =====
  
  /// Validate email format
  bool isEmailValid(String email) {
    _ensureInitialized();
    return _authRepository.isEmailValid(email);
  }
  
  /// Validate password strength
  bool isPasswordValid(String password) {
    _ensureInitialized();
    return _authRepository.isPasswordValid(password);
  }
  
  /// Get password requirements
  Map<String, dynamic> getPasswordRequirements() {
    _ensureInitialized();
    return _authRepository.getPasswordRequirements();
  }
  
  // ===== CONVENIENCE METHODS =====
  
  /// Check if user has access to a specific feature
  bool hasAccessTo(String feature) {
    _ensureInitialized();
    return currentUser?.hasAccessTo(feature) ?? false;
  }
  
  /// Get user role display name
  String get userRoleDisplayName {
    _ensureInitialized();
    return currentUser?.roleDisplayName ?? 'Unknown';
  }
  
  /// Get user display name
  String get userDisplayName {
    _ensureInitialized();
    return currentUser?.displayName ?? 'Unknown User';
  }
  
  /// Check if user is admin
  bool get isAdmin {
    _ensureInitialized();
    return currentUser?.isAdminYayasan ?? false;
  }
  
  /// Check if user is kepala dapur
  bool get isKepalaDapur {
    _ensureInitialized();
    return currentUser?.isKepalaDapur ?? false;
  }
  
  /// Check if user is ahli gizi
  bool get isAhliGizi {
    _ensureInitialized();
    return currentUser?.isAhliGizi ?? false;
  }
  
  /// Check if user is akuntan
  bool get isAkuntan {
    _ensureInitialized();
    return currentUser?.isAkuntan ?? false;
  }
  
  /// Check if user is pengawas
  bool get isPengawasPemeliharaan {
    _ensureInitialized();
    return currentUser?.isPengawasPemeliharaan ?? false;
  }
  
  /// Check if user is guest
  bool get isGuest {
    _ensureInitialized();
    return currentUser?.isGuest ?? true;
  }
  
  // ===== UTILITIES =====
  
  /// Ensure service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('AuthService not initialized. Call initialize() first.');
    }
  }
  
  /// Dispose auth service
  void dispose() {
    _logger.d('Disposing AuthService');
    
    if (_isInitialized) {
      _authRepository.dispose();
      _isInitialized = false;
    }
    
    _instance = null;
  }
}
