import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_form.dart';
import '../../../../app/widgets/app_notifications.dart';
import '../../../../app/widgets/responsive_layout.dart';
import '../../../../app/config/app_router.dart';
import '../../../../core/auth/presentation/auth_widgets.dart';
import '../../../../core/auth/domain/auth_state.dart';

/// Enhanced login page dengan Supabase authentication
class EnhancedLoginPage extends StatefulWidget {
  const EnhancedLoginPage({super.key});

  @override
  State<EnhancedLoginPage> createState() => _EnhancedLoginPageState();
}

class _EnhancedLoginPageState extends State<EnhancedLoginPage> 
    with AuthMixin<EnhancedLoginPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  String _selectedRole = 'kepala_dapur';
  
  // Login mode: 'email' atau 'anonymous'
  String _loginMode = 'email';

  final List<Map<String, dynamic>> _roles = [
    {
      'value': 'admin_yayasan',
      'label': 'Admin Yayasan',
      'icon': AppIcons.adminYayasan,
      'color': AppColors.adminYayasanColor,
    },
    {
      'value': 'perwakilan_yayasan',
      'label': 'Perwakilan Yayasan',
      'icon': AppIcons.perwakilanYayasan,
      'color': AppColors.perwakilanYayasanColor,
    },
    {
      'value': 'kepala_dapur',
      'label': 'Kepala Dapur SPPG',
      'icon': AppIcons.kepalaDapur,
      'color': AppColors.kepalaDapurColor,
    },
    {
      'value': 'ahli_gizi',
      'label': 'Ahli Gizi',
      'icon': AppIcons.ahliGizi,
      'color': AppColors.ahliGiziColor,
    },
    {
      'value': 'akuntan',
      'label': 'Akuntan',
      'icon': AppIcons.akuntan,
      'color': AppColors.akuntanColor,
    },
    {
      'value': 'pengawas_pemeliharaan',
      'label': 'Pengawas Pemeliharaan & Penghantaran',
      'icon': AppIcons.pengawasPemeliharaan,
      'color': AppColors.pengawasPemeliharaanColor,
    },
  ];

  @override
  void initState() {
    super.initState();
    _logger.d('EnhancedLoginPage initialized');
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  void onAuthStateChanged(AuthState state) {
    super.onAuthStateChanged(state);
    
    if (state.isAuthenticated || state.isAnonymous) {
      // Navigate to dashboard on successful login
      AppRouter.goToDashboard(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building EnhancedLoginPage');
    
    return AuthBuilder(
      onAuthStateChanged: onAuthStateChanged,
      builder: (context, authState) {
        return Scaffold(
          body: Container(
            decoration: _buildBackgroundDecoration(),
            child: ResponsiveLayout(
              mobile: _buildMobileLayout(),
              tablet: _buildTabletLayout(),
              desktop: _buildDesktopLayout(),
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryLight,
          AppColors.primary,
          AppColors.primaryDark,
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Welcome section
        Expanded(
          flex: 2,
          child: _buildWelcomeSection(),
        ),
        // Right side - Login form
        Expanded(
          flex: 1,
          child: _buildLoginSection(),
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: _buildWelcomeSection(),
        ),
        Expanded(
          flex: 2,
          child: _buildLoginSection(),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildMobileHeader(),
          const SizedBox(height: AppSpacing.xl),
          _buildLoginSection(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo dan branding
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(radius.AppRadius.large),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: const Icon(
                  AppIcons.kitchen,
                  size: 40,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: AppSpacing.lg),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h1.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Sistem Operasional Dapur',
                    style: AppTypography.h4.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Feature highlights
          _buildFeatureHighlights(),
        ],
      ),
    );
  }

  Widget _buildFeatureHighlights() {
    final features = [
      {
        'icon': AppIcons.dashboard,
        'title': 'Dashboard Terpusat',
        'description': 'Monitoring real-time semua operasi dapur',
      },
      {
        'icon': AppIcons.inventory,
        'title': 'Manajemen Inventaris',
        'description': 'Kontrol stok bahan baku secara akurat',
      },
      {
        'icon': AppIcons.logistics,
        'title': 'Distribusi & Logistik',
        'description': 'Tracking pengiriman dengan GPS',
      },
      {
        'icon': AppIcons.financial,
        'title': 'Keuangan Terintegrasi',
        'description': 'Laporan keuangan yang transparan',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.lg),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(radius.AppRadius.medium),
                ),
                child: Icon(
                  feature['icon'] as IconData,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feature['title'] as String,
                      style: AppTypography.h5.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      feature['description'] as String,
                      style: AppTypography.body2.copyWith(
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMobileHeader() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.xxl),
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(radius.AppRadius.large),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: const Icon(
              AppIcons.kitchen,
              size: 50,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            'SOD-MBG',
            style: AppTypography.h2.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Sistem Operasional Dapur MBG',
            style: AppTypography.h5.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginSection() {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLoginCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginCard() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 400),
      padding: const EdgeInsets.all(AppSpacing.xl),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(radius.AppRadius.large),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: AppSpacing.xl),
          _buildLoginModeSelector(),
          const SizedBox(height: AppSpacing.lg),
          _buildLoginForm(),
          const SizedBox(height: AppSpacing.lg),
          _buildOfflineIndicator(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Selamat Datang',
          style: AppTypography.h3.copyWith(
            color: AppColors.primaryDark,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Masuk ke akun Anda untuk melanjutkan',
          style: AppTypography.body1.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginModeSelector() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(radius.AppRadius.medium),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _loginMode = 'email'),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.md,
                  vertical: AppSpacing.sm,
                ),
                decoration: BoxDecoration(
                  color: _loginMode == 'email' ? AppColors.primary : Colors.transparent,
                  borderRadius: BorderRadius.circular(radius.AppRadius.medium),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.email_outlined,
                      color: _loginMode == 'email' ? Colors.white : AppColors.textSecondary,
                      size: 20,
                    ),
                    const SizedBox(width: AppSpacing.sm),
                    Text(
                      'Email',
                      style: AppTypography.body2.copyWith(
                        color: _loginMode == 'email' ? Colors.white : AppColors.textSecondary,
                        fontWeight: _loginMode == 'email' ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _loginMode = 'anonymous'),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.md,
                  vertical: AppSpacing.sm,
                ),
                decoration: BoxDecoration(
                  color: _loginMode == 'anonymous' ? AppColors.primary : Colors.transparent,
                  borderRadius: BorderRadius.circular(radius.AppRadius.medium),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person_outline,
                      color: _loginMode == 'anonymous' ? Colors.white : AppColors.textSecondary,
                      size: 20,
                    ),
                    const SizedBox(width: AppSpacing.sm),
                    Text(
                      'Guest',
                      style: AppTypography.body2.copyWith(
                        color: _loginMode == 'anonymous' ? Colors.white : AppColors.textSecondary,
                        fontWeight: _loginMode == 'anonymous' ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          if (_loginMode == 'email') ...[
            _buildRoleSelector(),
            const SizedBox(height: AppSpacing.lg),
            _buildEmailField(),
            const SizedBox(height: AppSpacing.md),
            _buildPasswordField(),
            const SizedBox(height: AppSpacing.sm),
            _buildForgotPasswordButton(),
            const SizedBox(height: AppSpacing.lg),
            _buildLoginButton(),
          ] else ...[
            _buildAnonymousInfo(),
            const SizedBox(height: AppSpacing.lg),
            _buildAnonymousLoginButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildRoleSelector() {
    return AppDropdownField<String>(
      value: _selectedRole,
      label: 'Peran',
      items: _roles.map((role) {
        return DropdownMenuItem<String>(
          value: role['value'],
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: (role['color'] as Color).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(radius.AppRadius.small),
                ),
                child: Icon(
                  role['icon'] as IconData,
                  color: role['color'] as Color,
                  size: 18,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Text(
                  role['label'] as String,
                  style: AppTypography.body2,
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedRole = value;
          });
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Silakan pilih peran';
        }
        return null;
      },
    );
  }

  Widget _buildEmailField() {
    return AppFormField(
      controller: _emailController,
      label: 'Email',
      hint: 'Masukkan email Anda',
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      prefixIcon: Icons.email_outlined,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Email tidak boleh kosong';
        }
        if (!authService.isEmailValid(value)) {
          return 'Format email tidak valid';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return AppFormField(
      controller: _passwordController,
      label: 'Password',
      hint: 'Masukkan password Anda',
      obscureText: _obscurePassword,
      textInputAction: TextInputAction.done,
      prefixIcon: Icons.lock_outline,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password tidak boleh kosong';
        }
        if (value.length < 6) {
          return 'Password minimal 6 karakter';
        }
        return null;
      },
      onFieldSubmitted: (_) => _handleEmailLogin(),
    );
  }

  Widget _buildForgotPasswordButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: AppButtonFactory.text(
        text: 'Lupa Password?',
        onPressed: _handleForgotPassword,
      ),
    );
  }

  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      child: AppButtonFactory.primary(
        text: 'Masuk',
        onPressed: _handleEmailLogin,
        isLoading: _isLoading,
      ),
    );
  }

  Widget _buildAnonymousInfo() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.warningLight,
        borderRadius: BorderRadius.circular(radius.AppRadius.medium),
        border: Border.all(color: AppColors.warning),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.warning,
                size: 20,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                'Mode Guest',
                style: AppTypography.body2.copyWith(
                  color: AppColors.warning,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Anda akan masuk sebagai guest dengan akses terbatas. '
            'Beberapa fitur mungkin tidak tersedia dalam mode ini.',
            style: AppTypography.body2.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnonymousLoginButton() {
    return SizedBox(
      width: double.infinity,
      child: AppButtonFactory.outline(
        text: 'Masuk sebagai Guest',
        onPressed: _handleAnonymousLogin,
        isLoading: _isLoading,
      ),
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: AppColors.successLight,
        borderRadius: BorderRadius.circular(radius.AppRadius.small),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.wifi_off,
            color: AppColors.success,
            size: 16,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            'Offline Mode Available',
            style: AppTypography.caption.copyWith(
              color: AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  // ===== EVENT HANDLERS =====

  void _handleEmailLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await authService.signInWithEmail(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (result is AuthErrorState) {
        if (mounted) {
          AppNotifications.showSnackBar(
            context,
            message: result.error,
            type: NotificationType.error,
          );
        }
      } else if (result is AuthenticatedState || result is AnonymousState) {
        // Login successful, navigation akan ditangani oleh AuthBuilder
        _logger.i('Login successful');
      }
    } catch (e) {
      _logger.e('Login error: $e');
      if (mounted) {
        AppNotifications.showSnackBar(
          context,
          message: 'Terjadi kesalahan saat login',
          type: NotificationType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _handleAnonymousLogin() async {
    setState(() => _isLoading = true);

    try {
      final result = await authService.signInAnonymously();

      if (result is AuthErrorState) {
        if (mounted) {
          AppNotifications.showSnackBar(
            context,
            message: result.error,
            type: NotificationType.error,
          );
        }
      } else if (result is AnonymousState) {
        // Anonymous login successful, navigation akan ditangani oleh AuthBuilder
        _logger.i('Anonymous login successful');
      }
    } catch (e) {
      _logger.e('Anonymous login error: $e');
      if (mounted) {
        AppNotifications.showSnackBar(
          context,
          message: 'Terjadi kesalahan saat login guest',
          type: NotificationType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _handleForgotPassword() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lupa Password'),
        content: const Text(
          'Fitur reset password akan segera tersedia. '
          'Silakan hubungi administrator untuk bantuan.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
