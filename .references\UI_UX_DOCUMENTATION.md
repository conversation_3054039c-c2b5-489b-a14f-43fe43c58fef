# Dokumentasi Sistem UI/UX & Alur Kerja SOD-MBG

## Gambaran Umum

Sistem UI/UX & Alur Kerja SOD-MBG merupakan implementasi komprehensif dari design system yang dirancang khusus untuk aplikasi Sistem Operasional Dapur MBG (SOD-MBG). Sistem ini menyediakan komponen-komponen UI yang konsisten, responsif, dan mudah digunakan untuk mendukung operasional dapur dan manajemen gizi.

## Struktur Komponen

### 1. Sistem Notifikasi (`app_notifications.dart`)

Menyediakan berbagai jenis notifikasi yang konsisten dengan design system:

#### `AppNotifications`
- **SnackBar**: Notifikasi sementara di bagian bawah layar
- **Banner**: Notifikasi persisten di bagian atas konten
- **Dialog**: Modal notification untuk informasi penting
- **Bottom Sheet**: Notifikasi dalam bentuk sheet yang muncul dari bawah
- **Toast**: Notifikasi floating di bagian atas layar

#### `AppInlineNotification`
- Notifikasi yang terintegrasi dalam konten halaman
- Mendukung actions dan dismissible

#### `AppToast`
- Notifikasi dengan animasi slide dan fade
- Auto-dismiss dengan durasi yang dapat dikonfigurasi

#### `AppAlertCard`
- Card khusus untuk alert dan peringatan
- Mendukung multiple actions dan rich content

```dart
// Contoh penggunaan
AppNotifications.showSnackBar(
  context,
  message: 'Data berhasil disimpan',
  type: NotificationType.success,
);

AppNotifications.showNotificationDialog(
  context,
  title: 'Konfirmasi',
  message: 'Apakah Anda yakin ingin menghapus data ini?',
  type: NotificationType.warning,
);
```

### 2. Sistem Form (`app_form.dart`)

Komponen form yang komprehensif untuk input data:

#### `AppFormField`
- Text input field dengan validasi dan styling konsisten
- Mendukung berbagai jenis input (text, email, password, number, dll.)
- Fitur: label, helper text, error message, prefix/suffix icon
- Auto-validation dan custom validation

#### `AppDropdownField<T>`
- Dropdown selection dengan type safety
- Styling yang konsisten dengan form field lainnya
- Mendukung validasi dan error handling

#### `AppCheckboxField`
- Checkbox dengan label dan description
- Styling yang konsisten dan accessible

#### `AppRadioField<T>`
- Radio button group dengan type safety
- Mendukung layout vertical dan horizontal
- Rich content dengan title dan description

#### `AppFormSection`
- Organisasi form dalam section dengan divider
- Title dan subtitle untuk setiap section

#### `AppFormActions`
- Tombol actions yang konsisten untuk form
- Mendukung Cancel, Save, dan Submit
- Loading state dan disabled state

```dart
// Contoh penggunaan
AppFormField(
  label: 'Nama Bahan',
  hint: 'Masukkan nama bahan',
  isRequired: true,
  controller: _nameController,
  validator: (value) {
    if (value?.isEmpty ?? true) {
      return 'Nama bahan tidak boleh kosong';
    }
    return null;
  },
)
```

### 3. Sistem Navigasi (`app_navigation.dart`)

Komponen navigasi yang lengkap untuk berbagai kebutuhan:

#### `AppBottomNavigationBar`
- Bottom navigation dengan styling yang konsisten
- Mendukung icon dan label
- Active/inactive state yang jelas

#### `AppNavigationRail`
- Navigation rail untuk layout yang lebih luas
- Extended mode untuk label yang lebih jelas
- Leading dan trailing widgets

#### `AppDrawer`
- Sidebar navigation dengan header customizable
- Mendukung divider dan header section
- Selected state dan trailing widgets

#### `AppBreadcrumb`
- Breadcrumb navigation untuk hierarki halaman
- Clickable items dan custom separator

#### `AppTabBar`
- Tab bar dengan styling yang konsisten
- Scrollable tabs untuk banyak tab
- Icon dan text support

#### `AppTopBar`
- App bar dengan styling yang konsisten
- Customizable title, actions, dan leading

#### `AppStepperNavigation`
- Stepper untuk workflow yang berurutan
- Custom controls dan step state management

```dart
// Contoh penggunaan
AppBottomNavigationBar(
  currentIndex: _currentIndex,
  onTap: (index) => setState(() => _currentIndex = index),
  items: [
    AppNavigationItem(
      icon: AppIcons.dashboard,
      label: 'Dashboard',
    ),
    AppNavigationItem(
      icon: AppIcons.inventory,
      label: 'Inventori',
    ),
  ],
)
```

### 4. Sistem Data Display (`app_data_display.dart`)

Komponen untuk menampilkan data dalam berbagai format:

#### `AppListItem`
- List item yang konsisten dengan leading/trailing
- Selected state dan custom styling
- Clickable dengan ripple effect

#### `AppExpandableListItem`
- List item yang dapat diperluas untuk detail
- Smooth animation dan state management
- Custom leading dan content

#### `AppDataTable`
- Data table dengan horizontal scroll
- Sorting dan custom column configuration
- Responsive design

#### `AppPaginatedDataTable`
- Data table dengan pagination
- Custom page size dan navigation
- Sorting dan filtering support

#### `AppGridView`
- Grid layout dengan spacing yang konsisten
- Customizable aspect ratio dan spacing
- Responsive column count

#### `AppKitchenInventoryItem`
- Komponen khusus untuk inventori dapur
- Status indicator dan actions
- Rich information display

#### `AppLoadingList`
- Skeleton loading untuk list
- Customizable item count dan height
- Smooth loading animation

```dart
// Contoh penggunaan
AppKitchenInventoryItem(
  itemName: 'Beras Premium',
  description: 'Beras putih kualitas terbaik',
  quantity: '50',
  unit: 'kg',
  expiryDate: '2024-12-31',
  status: 'available',
  onEdit: () => _editItem(),
  onDelete: () => _deleteItem(),
)
```

### 5. Sistem Responsive Layout (`responsive_layout.dart`)

Komponen untuk layout yang responsif di berbagai ukuran layar:

#### `ResponsiveLayout`
- Layout yang menyesuaikan dengan ukuran layar
- Breakpoint-based layout switching
- Mobile, tablet, dan desktop support

#### `ResponsivePadding`
- Padding yang menyesuaikan dengan ukuran layar
- Consistent spacing across devices

#### `ResponsiveGrid`
- Grid yang menyesuaikan kolom berdasarkan layar
- Automatic column calculation

#### `ResponsiveHeader`
- Header yang menyesuaikan dengan ukuran layar
- Different layout for mobile and desktop

#### `ResponsiveCard`
- Card dengan layout yang responsif
- Adaptive content arrangement

#### `ResponsiveNavigation`
- Navigation yang berubah berdasarkan ukuran layar
- Bottom nav untuk mobile, rail untuk desktop

```dart
// Contoh penggunaan
ResponsiveLayout(
  mobile: MobileLayout(),
  tablet: TabletLayout(),
  desktop: DesktopLayout(),
)
```

### 6. Sistem Workflow (`workflow_components.dart`)

Komponen khusus untuk manajemen workflow dapur:

#### `WorkflowStatus`
- Status indicator untuk workflow
- Color-coded berdasarkan status
- Progress percentage

#### `WorkflowTimeline`
- Timeline untuk tracking progress
- Step-by-step visualization
- Time stamps dan status

#### `WorkflowControls`
- Tombol kontrol untuk workflow
- Previous/Next navigation
- Save/Submit actions

#### `WorkflowTaskCard`
- Card untuk menampilkan task
- Status indicator dan actions
- Rich content dengan metadata

```dart
// Contoh penggunaan
WorkflowStatus(
  status: 'in_progress',
  progress: 0.6,
  title: 'Sedang Memasak',
)
```

### 7. Sistem State Management (`app_states.dart`)

Komponen untuk berbagai state aplikasi:

#### `AppLoadingState`
- Loading state dengan spinner dan message
- Customizable loading indicators

#### `AppEmptyState`
- Empty state dengan illustration dan actions
- Customizable message dan call-to-action

#### `AppErrorState`
- Error state dengan error message dan retry
- Different error types handling

#### `AppOfflineState`
- Offline state dengan connectivity info
- Retry mechanism

#### `AppSuccessState`
- Success state dengan confirmation
- Actions dan next steps

#### `AppMaintenanceState`
- Maintenance mode state
- Scheduled maintenance info

#### `AppShimmerLoading`
- Shimmer loading effect
- Customizable dimensions dan count

#### `AppProgressState`
- Progress state dengan percentage
- Step-by-step progress tracking

```dart
// Contoh penggunaan
AppLoadingState(
  message: 'Mengambil data inventori...',
  showProgress: true,
)
```

## Fitur Utama

### 1. **Konsistensi Design**
- Semua komponen menggunakan design system yang sama
- Color palette, typography, dan spacing yang konsisten
- Icon set yang unified

### 2. **Responsivitas**
- Support untuk mobile, tablet, dan desktop
- Breakpoint-based layout switching
- Adaptive spacing dan sizing

### 3. **Aksesibilitas**
- Semantic markup yang tepat
- Keyboard navigation support
- Screen reader compatibility
- High contrast dan large text support

### 4. **Performa**
- Lazy loading untuk data yang besar
- Efficient widget rebuilding
- Optimized untuk operasional dapur

### 5. **Offline Support**
- Komponen yang dapat bekerja offline
- Sync state management
- Queue untuk actions offline

### 6. **Kitchen-Specific Features**
- Komponen khusus untuk operasional dapur
- Inventory management UI
- Workflow tracking
- QC status indicators

## Integrasi dengan Sistem

### 1. **PowerSync Integration**
- Offline-first data synchronization
- Real-time updates ketika online
- Conflict resolution

### 2. **Supabase Backend**
- Authentication state management
- Real-time subscriptions
- File upload untuk foto

### 3. **BLoC State Management**
- Reactive state management
- Business logic separation
- Event-driven architecture

### 4. **GoRouter Navigation**
- Declarative navigation
- Deep linking support
- Route guards

## Penggunaan dan Best Practices

### 1. **Konsistensi**
- Selalu gunakan komponen yang sudah tersedia
- Ikuti naming convention yang established
- Gunakan design tokens dari constants

### 2. **Performa**
- Gunakan const constructor kapanpun memungkinkan
- Lazy load untuk data yang besar
- Optimize untuk rebuild frequency

### 3. **Maintenance**
- Dokumentasikan custom widgets
- Gunakan type-safe APIs
- Follow single responsibility principle

### 4. **Testing**
- Write unit tests untuk business logic
- Widget tests untuk UI components
- Integration tests untuk user flows

## Roadmap

### Phase 1 (Completed)
- ✅ Core UI components
- ✅ Responsive layout system
- ✅ Notification system
- ✅ Form system
- ✅ Navigation components
- ✅ Data display components
- ✅ Workflow components
- ✅ State management components

### Phase 2 (Next)
- [ ] Advanced data visualization
- [ ] Custom animations
- [ ] Accessibility improvements
- [ ] Performance optimizations
- [ ] Theme customization
- [ ] Component documentation
- [ ] Storybook implementation

### Phase 3 (Future)
- [ ] Advanced offline capabilities
- [ ] Real-time collaboration
- [ ] Analytics integration
- [ ] Advanced workflow automation
- [ ] Multi-language support
- [ ] Advanced theming
- [ ] Component library packaging

## Kesimpulan

Sistem UI/UX & Alur Kerja SOD-MBG menyediakan foundation yang kuat untuk pengembangan aplikasi dapur yang modern, responsif, dan user-friendly. Dengan komponen yang lengkap dan design system yang konsisten, pengembangan fitur-fitur baru akan menjadi lebih cepat dan maintainable.

Semua komponen telah dirancang dengan mempertimbangkan kebutuhan spesifik operasional dapur, mulai dari inventory management hingga workflow tracking, dengan tetap mempertahankan standar UI/UX yang tinggi untuk semua jenis pengguna.
