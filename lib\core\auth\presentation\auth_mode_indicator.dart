import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../app/constants/app_colors.dart';
import '../../../app/constants/app_typography.dart';
import '../../../app/constants/app_spacing.dart';
import '../../../app/constants/app_icons.dart';
import 'auth_service.dart';

/// Widget to show authentication mode status
class AuthModeIndicator extends StatelessWidget {
  final bool showDetails;
  final VoidCallback? onTap;

  const AuthModeIndicator({
    super.key,
    this.showDetails = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final authService = AuthService.instance;
    
    if (!authService.isInitialized) {
      return const SizedBox.shrink();
    }

    if (!authService.isMockMode) {
      return const SizedBox.shrink(); // Don't show indicator for normal mode
    }

    return GestureDetector(
      onTap: onTap ?? () => _showMockModeInfo(context),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: AppColors.warning.withOpacity(0.1),
          border: Border.all(
            color: AppColors.warning,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              AppIcons.warning,
              size: 16,
              color: AppColors.warning,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              'Development Mode',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.warning,
                fontWeight: AppTypography.semiBold,
              ),
            ),
            if (showDetails) ...[
              const SizedBox(width: AppSpacing.sm),
              Icon(
                AppIcons.info,
                size: 12,
                color: AppColors.warning,
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showMockModeInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              AppIcons.warning,
              color: AppColors.warning,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              'Development Mode',
              style: AppTypography.h6.copyWith(
                color: AppColors.warning,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'The application is running in development mode with mock authentication.',
              style: AppTypography.bodyMedium,
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'Features in mock mode:',
              style: AppTypography.bodyMedium.copyWith(
                fontWeight: AppTypography.semiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            ...const [
              '• Registration creates mock accounts',
              '• Login works with any registered email',
              '• Data is stored locally only',
              '• No real server connection',
            ].map((text) => Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.xs),
              child: Text(text, style: AppTypography.bodySmall),
            )),
            const SizedBox(height: AppSpacing.md),
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: AppColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'To use real authentication, please configure Supabase credentials. See SUPABASE_SETUP.md for instructions.',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.info,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Got it',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Banner widget to show at the top of authentication pages
class AuthModeBanner extends StatelessWidget {
  const AuthModeBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = AuthService.instance;
    
    if (!authService.isInitialized || !authService.isMockMode) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.warning.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: AppColors.warning.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            AppIcons.warning,
            size: 20,
            color: AppColors.warning,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Development Mode Active',
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.warning,
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
                Text(
                  'Using mock authentication. Configure Supabase for production.',
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.warning,
                  ),
                ),
              ],
            ),
          ),
          AuthModeIndicator(
            showDetails: true,
            onTap: () => Logger().i('Mock mode banner tapped'),
          ),
        ],
      ),
    );
  }
}
