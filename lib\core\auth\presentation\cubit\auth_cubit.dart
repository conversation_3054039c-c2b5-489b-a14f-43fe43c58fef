import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:aplikasi_sppg/core/auth/domain/app_user.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import '../../domain/auth_state.dart' as domain;

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthService _authService;

  AuthCubit(this._authService) : super(AuthInitial()) {
    _authService.authStateStream.listen((authState) {
      if (authState is domain.AuthenticatedState) {
        emit(Authenticated(authState.user));
      } else if (authState is domain.UnauthenticatedState) {
        emit(Unauthenticated());
      } else if (authState is domain.AuthErrorState) {
        emit(AuthError(authState.error));
      } else if (authState is domain.AuthLoadingState) {
        emit(AuthLoading());
      }
    });
  }

  void checkAuthentication() {
    final currentState = _authService.currentAuthState;
    if (currentState is domain.AuthenticatedState) {
      emit(Authenticated(currentState.user));
    } else {
      emit(Unauthenticated());
    }
  }

  Future<void> signOut() async {
    await _authService.signOut();
    emit(Unauthenticated());
  }
}
