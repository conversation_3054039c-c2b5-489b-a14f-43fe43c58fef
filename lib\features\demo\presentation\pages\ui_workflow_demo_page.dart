import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../qc/presentation/pages/qc_daily_page.dart';
import '../../../delivery/presentation/pages/delivery_tracking_page.dart';
import 'color_scheme_demo_page.dart';
import '../../../admin/presentation/pages/admin_dashboard_page.dart';

/// Halaman navigasi untuk demo UI/UX & Alur Kerja
/// Menampilkan 3 role utama yang telah diimplementasikan
class UIWorkflowDemoPage extends StatefulWidget {
  const UIWorkflowDemoPage({super.key});

  @override
  State<UIWorkflowDemoPage> createState() => _UIWorkflowDemoPageState();
}

class _UIWorkflowDemoPageState extends State<UIWorkflowDemoPage> {
  final Logger _logger = Logger();

  @override
  void initState() {
    super.initState();
    _logger.i('UIWorkflowDemoPage initialized');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Demo UI/UX & Alur Kerja'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildHeader(),
              _buildRoleCards(),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppRadius.lg),
          bottomRight: Radius.circular(AppRadius.lg),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sistem Operasional Dapur MBG',
            style: AppTypography.h4.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Demo implementasi UI/UX & Alur Kerja untuk 3 role utama',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleCards() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pilih Role untuk Demo',
            style: AppTypography.h5,
          ),
          const SizedBox(height: AppSpacing.md),
          
          // Kepala Dapur SPPG
          _buildRoleCard(
            title: 'Kepala Dapur SPPG',
            subtitle: 'Dashboard dan QC Harian',
            description: 'Kelola operasional dapur sehari-hari, termasuk proses Quality Control untuk Test Food.',
            icon: AppIcons.kepalaDapur,
            color: AppColors.kepalaDapurColor,
            onTap: () => _navigateToQcDaily(),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Pengawas Pemeliharaan
          _buildRoleCard(
            title: 'Pengawas Pemeliharaan',
            subtitle: 'Tracking Pengiriman',
            description: 'Pantau dan dokumentasikan pengiriman makanan dengan foto sebagai bukti.',
            icon: AppIcons.pengawasPemeliharaan,
            color: AppColors.pengawasPemeliharaanColor,
            onTap: () => _navigateToDeliveryTracking(),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Color Scheme Demo
          _buildRoleCard(
            title: 'Demo Skema Warna',
            subtitle: 'YellowBlueSkyHappy Theme',
            description: 'Lihat implementasi skema warna baru yang hangat dan menenangkan.',
            icon: AppIcons.settings,
            color: AppColors.primary,
            onTap: () => _navigateToColorDemo(),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Admin Yayasan
          _buildRoleCard(
            title: 'Admin Yayasan',
            subtitle: 'Dashboard Eksekutif',
            description: 'Pantau total porsi terdistribusi dan ringkasan operasional semua SPPG.',
            icon: AppIcons.adminYayasan,
            color: AppColors.adminYayasanColor,
            onTap: () => _navigateToAdminDashboard(),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleCard({
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return AppCardFactory.basic(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppRadius.card),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppRadius.sm),
                    ),
                    child: Icon(
                      icon,
                      size: AppIcons.sizeLarge,
                      color: color,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: AppTypography.h6.copyWith(
                            color: color,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          subtitle,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: AppIcons.sizeSmall,
                    color: AppColors.textTertiary,
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                description,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: AppCardFactory.basic(
        child: Column(
          children: [
            Icon(
              AppIcons.info,
              size: AppIcons.sizeMedium,
              color: AppColors.infoBlue,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Demo UI/UX & Alur Kerja',
              style: AppTypography.h6.copyWith(
                color: AppColors.infoBlue,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Ini adalah implementasi demo untuk menunjukkan desain UI/UX dan alur kerja dari 3 role utama dalam sistem SOD-MBG.',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToQcDaily() {
    _logger.i('Navigating to QC Daily page');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const QcDailyPage(),
      ),
    );
  }

  void _navigateToDeliveryTracking() {
    _logger.i('Navigating to Delivery Tracking page');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DeliveryTrackingPage(),
      ),
    );
  }

  void _navigateToColorDemo() {
    _logger.i('Navigating to Color Scheme Demo page');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ColorSchemeDemoPage(),
      ),
    );
  }

  void _navigateToAdminDashboard() {
    _logger.i('Navigating to Admin Dashboard page');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AdminDashboardPage(),
      ),
    );
  }
}
