import 'package:equatable/equatable.dart';

/// Model untuk user dalam sistem SOD-MBG
/// Extends Equatable untuk comparison yang mudah
class AppUser extends Equatable {
  const AppUser({
    required this.id,
    required this.email,
    required this.role,
    this.nama,
    this.sppgId,
    this.sppgName,
    this.isAnonymous = false,
    this.createdAt,
    this.lastLoginAt,
    this.metadata,
  });

  /// User ID dari Supabase Auth
  final String id;
  
  /// Email user (nullable untuk anonymous)
  final String? email;
  
  /// Nama lengkap user
  final String? nama;
  
  /// Role user dalam sistem (admin_yayasan, kepala_dapur, dll)
  final String role;
  
  /// ID SPPG tempat user bertugas (nullable untuk admin)
  final String? sppgId;
  
  /// Nama SPPG tempat user bertugas
  final String? sppgName;
  
  /// Flag untuk menandai anonymous user
  final bool isAnonymous;
  
  /// Timestamp pembuatan user
  final DateTime? createdAt;
  
  /// Timestamp login terakhir
  final DateTime? lastLoginAt;
  
  /// Metadata tambahan
  final Map<String, dynamic>? metadata;
  
  // ===== HELPER METHODS =====
  
  /// Check apakah user adalah admin yayasan
  bool get isAdminYayasan => role == 'admin_yayasan';
  
  /// Check apakah user adalah perwakilan yayasan
  bool get isPerwakilanYayasan => role == 'perwakilan_yayasan';
  
  /// Check apakah user adalah kepala dapur
  bool get isKepalaDapur => role == 'kepala_dapur';
  
  /// Check apakah user adalah ahli gizi
  bool get isAhliGizi => role == 'ahli_gizi';
  
  /// Check apakah user adalah akuntan
  bool get isAkuntan => role == 'akuntan';
  
  /// Check apakah user adalah pengawas
  bool get isPengawasPemeliharaan => role == 'pengawas_pemeliharaan';
  
  /// Check apakah user adalah guest/anonymous
  bool get isGuest => isAnonymous || role == 'guest';
  
  /// Get display name untuk user
  String get displayName {
    if (isAnonymous) {
      return 'Guest User';
    }
    return nama ?? email ?? 'Unknown User';
  }
  
  /// Get role display name
  String get roleDisplayName {
    switch (role) {
      case 'admin_yayasan':
        return 'Admin Yayasan';
      case 'perwakilan_yayasan':
        return 'Perwakilan Yayasan';
      case 'kepala_dapur':
        return 'Kepala Dapur SPPG';
      case 'ahli_gizi':
        return 'Ahli Gizi';
      case 'akuntan':
        return 'Akuntan';
      case 'pengawas_pemeliharaan':
        return 'Pengawas Pemeliharaan & Penghantaran';
      case 'guest':
        return 'Guest';
      default:
        return role;
    }
  }
  
  /// Check apakah user memiliki akses ke fitur tertentu
  bool hasAccessTo(String feature) {
    switch (feature) {
      case 'dashboard':
        return !isGuest;
      case 'kitchen_management':
        return isAdminYayasan || isPerwakilanYayasan || isKepalaDapur || isAhliGizi;
      case 'inventory':
        return isAdminYayasan || isPerwakilanYayasan || isKepalaDapur || isAhliGizi || isAkuntan;
      case 'logistics':
        return isAdminYayasan || isPerwakilanYayasan || isKepalaDapur || isPengawasPemeliharaan;
      case 'financial':
        return isAdminYayasan || isPerwakilanYayasan || isAkuntan;
      case 'reporting':
        return !isGuest;
      case 'user_management':
        return isAdminYayasan || isPerwakilanYayasan;
      case 'settings':
        return !isGuest;
      default:
        return false;
    }
  }
  
  // ===== COPY WITH =====
  
  AppUser copyWith({
    String? id,
    String? email,
    String? nama,
    String? role,
    String? sppgId,
    String? sppgName,
    bool? isAnonymous,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? metadata,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      nama: nama ?? this.nama,
      role: role ?? this.role,
      sppgId: sppgId ?? this.sppgId,
      sppgName: sppgName ?? this.sppgName,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      metadata: metadata ?? this.metadata,
    );
  }
  
  // ===== SERIALIZATION =====
  
  /// Convert to Map untuk penyimpanan
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'nama': nama,
      'role': role,
      'sppg_id': sppgId,
      'sppg_name': sppgName,
      'is_anonymous': isAnonymous,
      'created_at': createdAt?.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'metadata': metadata,
    };
  }
  
  /// Create from Map
  factory AppUser.fromMap(Map<String, dynamic> map) {
    return AppUser(
      id: map['id'] ?? '',
      email: map['email'],
      nama: map['nama'],
      role: map['role'] ?? 'guest',
      sppgId: map['sppg_id'],
      sppgName: map['sppg_name'],
      isAnonymous: map['is_anonymous'] ?? false,
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      lastLoginAt: map['last_login_at'] != null ? DateTime.parse(map['last_login_at']) : null,
      metadata: map['metadata'],
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() => toMap();
  
  /// Create from JSON
  factory AppUser.fromJson(Map<String, dynamic> json) => AppUser.fromMap(json);
  
  // ===== EQUATABLE =====
  
  @override
  List<Object?> get props => [
    id,
    email,
    nama,
    role,
    sppgId,
    sppgName,
    isAnonymous,
    createdAt,
    lastLoginAt,
    metadata,
  ];
  
  @override
  String toString() {
    return 'AppUser(id: $id, email: $email, nama: $nama, role: $role, sppgId: $sppgId, isAnonymous: $isAnonymous)';
  }
}

/// Extension untuk create anonymous user
extension AppUserExtension on AppUser {
  /// Create anonymous user
  static AppUser anonymous() {
    return AppUser(
      id: 'anonymous_${DateTime.now().millisecondsSinceEpoch}',
      email: null,
      nama: null,
      role: 'guest',
      isAnonymous: true,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
    );
  }
}
