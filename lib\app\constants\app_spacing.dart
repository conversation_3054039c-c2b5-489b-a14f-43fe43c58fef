/// Spacing system untuk Aplikasi SOD-MBG
/// Menggunakan spacing yang konsisten untuk layout yang rapi
class AppSpacing {
  // Private constructor
  AppSpacing._();

  // ===== BASE SPACING UNIT =====
  /// Unit dasar spacing (8px) - mengikuti Material Design guidelines
  static const double baseUnit = 8.0;

  // ===== MICRO SPACING =====
  /// Spacing sangat kecil - untuk elemen yang sangat dekat
  static const double xs = baseUnit * 0.5; // 4px

  // ===== SMALL SPACING =====
  /// Spacing kecil - untuk padding dalam komponen
  static const double sm = baseUnit * 1; // 8px

  // ===== MEDIUM SPACING =====
  /// Spacing sedang - spacing standar antar elemen
  static const double md = baseUnit * 2; // 16px

  // ===== LARGE SPACING =====
  /// Spacing besar - untuk pemisahan section
  static const double lg = baseUnit * 3; // 24px

  // ===== EXTRA LARGE SPACING =====
  /// Spacing sangat besar - untuk pemisahan major section
  static const double xl = baseUnit * 4; // 32px

  // ===== EXTRA EXTRA LARGE SPACING =====
  /// Spacing extra besar - untuk layout utama
  static const double xxl = baseUnit * 6; // 48px

  // ===== GIANT SPACING =====
  /// Spacing giant - untuk spacing halaman
  static const double giant = baseUnit * 8; // 64px

  // ===== KITCHEN SPECIFIC SPACING =====
  /// Spacing untuk tampilan dapur - lebih besar untuk visibilitas
  static const double kitchenSpacing = baseUnit * 5; // 40px
  static const double kitchenPadding = baseUnit * 3; // 24px
  static const double kitchenMargin = baseUnit * 4; // 32px

  // ===== CARD SPACING =====
  /// Padding dalam card
  static const double cardPadding = md; // 16px
  
  /// Margin antar card
  static const double cardMargin = md; // 16px
  
  /// Spacing antar elemen dalam card
  static const double cardSpacing = sm; // 8px

  // ===== FORM SPACING =====
  /// Spacing antar form field
  static const double formFieldSpacing = md; // 16px
  
  /// Padding dalam form field
  static const double formFieldPadding = sm; // 8px
  
  /// Margin form dari edge
  static const double formMargin = lg; // 24px

  // ===== BUTTON SPACING =====
  /// Padding horizontal button
  static const double buttonPaddingHorizontal = lg; // 24px
  
  /// Padding vertical button
  static const double buttonPaddingVertical = sm; // 8px
  
  /// Spacing antar button
  static const double buttonSpacing = sm; // 8px

  // ===== LIST SPACING =====
  /// Spacing antar item list
  static const double listItemSpacing = xs; // 4px
  
  /// Padding dalam list item
  static const double listItemPadding = md; // 16px
  
  /// Margin list dari container
  static const double listMargin = sm; // 8px

  // ===== NAVIGATION SPACING =====
  /// Padding navigation bar
  static const double navigationPadding = md; // 16px
  
  /// Spacing antar navigation item
  static const double navigationItemSpacing = lg; // 24px
  
  /// Height navigation bar
  static const double navigationHeight = baseUnit * 7; // 56px

  // ===== SCREEN SPACING =====
  /// Padding horizontal screen
  static const double screenPaddingHorizontal = md; // 16px
  
  /// Padding vertical screen
  static const double screenPaddingVertical = lg; // 24px
  
  /// Spacing antar section dalam screen
  static const double screenSectionSpacing = xl; // 32px

  // ===== DIALOG SPACING =====
  /// Padding dialog
  static const double dialogPadding = lg; // 24px
  
  /// Spacing antar elemen dalam dialog
  static const double dialogSpacing = md; // 16px
  
  /// Margin dialog dari edge
  static const double dialogMargin = xl; // 32px

  // ===== TABLET SPACING =====
  /// Spacing yang lebih besar untuk tablet
  static const double tabletPadding = xl; // 32px
  static const double tabletMargin = xxl; // 48px
  static const double tabletSectionSpacing = giant; // 64px

  // ===== DESKTOP SPACING =====
  /// Spacing untuk desktop
  static const double desktopPadding = xxl; // 48px
  static const double desktopMargin = giant; // 64px
  static const double desktopSectionSpacing = baseUnit * 10; // 80px
}

/// Breakpoints untuk responsive design
class AppBreakpoints {
  // Private constructor
  AppBreakpoints._();

  // ===== MOBILE BREAKPOINTS =====
  /// Mobile portrait
  static const double mobilePortrait = 0;
  
  /// Mobile landscape
  static const double mobileLandscape = 480;

  // ===== TABLET BREAKPOINTS =====
  /// Tablet portrait
  static const double tabletPortrait = 600;
  
  /// Tablet landscape
  static const double tabletLandscape = 900;

  // ===== DESKTOP BREAKPOINTS =====
  /// Desktop small
  static const double desktopSmall = 1200;
  
  /// Desktop medium
  static const double desktopMedium = 1600;
  
  /// Desktop large
  static const double desktopLarge = 1920;

  // ===== KITCHEN DISPLAY BREAKPOINTS =====
  /// Kitchen display - untuk monitor dapur
  static const double kitchenDisplay = 1366;
  
  /// Kitchen display large - untuk proyektor
  static const double kitchenDisplayLarge = 1920;

  // ===== UTILITY METHODS =====
  /// Check apakah screen mobile
  static bool isMobile(double width) => width < tabletPortrait;
  
  /// Check apakah screen tablet
  static bool isTablet(double width) => 
      width >= tabletPortrait && width < desktopSmall;
  
  /// Check apakah screen desktop
  static bool isDesktop(double width) => width >= desktopSmall;
  
  /// Check apakah screen kitchen display
  static bool isKitchenDisplay(double width) => width >= kitchenDisplay;

  /// Get responsive padding berdasarkan screen width
  static double getResponsivePadding(double screenWidth) {
    if (isDesktop(screenWidth)) {
      return AppSpacing.desktopPadding;
    } else if (isTablet(screenWidth)) {
      return AppSpacing.tabletPadding;
    } else {
      return AppSpacing.screenPaddingHorizontal;
    }
  }

  /// Get responsive margin berdasarkan screen width
  static double getResponsiveMargin(double screenWidth) {
    if (isDesktop(screenWidth)) {
      return AppSpacing.desktopMargin;
    } else if (isTablet(screenWidth)) {
      return AppSpacing.tabletMargin;
    } else {
      return AppSpacing.screenPaddingVertical;
    }
  }
}

/// Border radius system
class AppRadius {
  // Private constructor
  AppRadius._();

  // ===== BASIC RADIUS =====
  /// Radius kecil
  static const double sm = 4.0;
  
  /// Radius sedang
  static const double md = 8.0;
  
  /// Radius besar
  static const double lg = 12.0;
  
  /// Radius extra besar
  static const double xl = 16.0;
  
  /// Radius extra extra besar
  static const double xxl = 24.0;

  // ===== COMPONENT RADIUS =====
  /// Radius untuk button
  static const double button = md;
  
  /// Radius untuk card
  static const double card = lg;
  
  /// Radius untuk input field
  static const double input = md;
  
  /// Radius untuk dialog
  static const double dialog = xl;
  
  /// Radius untuk bottom sheet
  static const double bottomSheet = xl;

  // ===== KITCHEN RADIUS =====
  /// Radius untuk komponen dapur - lebih rounded untuk keamanan
  static const double kitchen = lg;
  
  /// Radius untuk kitchen card
  static const double kitchenCard = xl;

  // ===== SPECIAL RADIUS =====
  /// Circular (untuk avatar, dll)
  static const double circular = 100.0;
}

/// Elevation system untuk shadow
class AppElevation {
  // Private constructor
  AppElevation._();

  // ===== ELEVATION LEVELS =====
  /// Elevation level 0 - tanpa shadow
  static const double level0 = 0;
  
  /// Elevation level 1 - subtle shadow
  static const double level1 = 1;
  
  /// Elevation level 2 - card shadow
  static const double level2 = 2;
  
  /// Elevation level 3 - raised element
  static const double level3 = 4;
  
  /// Elevation level 4 - dialog shadow
  static const double level4 = 8;
  
  /// Elevation level 5 - floating action button
  static const double level5 = 12;

  // ===== COMPONENT ELEVATION =====
  /// Elevation untuk card
  static const double card = level2;
  
  /// Elevation untuk button
  static const double button = level1;
  
  /// Elevation untuk app bar
  static const double appBar = level1;
  
  /// Elevation untuk dialog
  static const double dialog = level4;
  
  /// Elevation untuk bottom sheet
  static const double bottomSheet = level3;
  
  /// Elevation untuk FAB
  static const double fab = level5;

  // ===== KITCHEN ELEVATION =====
  /// Elevation untuk kitchen component
  static const double kitchen = level2;
  
  /// Elevation untuk alert/warning
  static const double alert = level3;
}
