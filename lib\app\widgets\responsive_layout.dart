import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';

/// Komponen layout responsif untuk Aplikasi SOD-MBG
/// Menyediakan layout yang adaptif untuk berbagai ukuran layar
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? kitchenDisplay;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.kitchenDisplay,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;

        // Kitchen display untuk layar besar (>= 1366px)
        if (kitchenDisplay != null && AppBreakpoints.isKitchenDisplay(screenWidth)) {
          return kitchenDisplay!;
        }
        
        // Desktop untuk layar lebar (>= 1200px)
        if (desktop != null && AppBreakpoints.isDesktop(screenWidth)) {
          return desktop!;
        }
        
        // Tablet untuk layar sedang (>= 600px)
        if (tablet != null && AppBreakpoints.isTablet(screenWidth)) {
          return tablet!;
        }
        
        // Mobile sebagai fallback
        return mobile;
      },
    );
  }
}

/// Wrapper untuk memberikan responsive padding
class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsets? mobile;
  final EdgeInsets? tablet;
  final EdgeInsets? desktop;

  const ResponsivePadding({
    super.key,
    required this.child,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        
        EdgeInsets padding;
        if (AppBreakpoints.isDesktop(screenWidth)) {
          padding = desktop ?? const EdgeInsets.all(AppSpacing.desktopPadding);
        } else if (AppBreakpoints.isTablet(screenWidth)) {
          padding = tablet ?? const EdgeInsets.all(AppSpacing.tabletPadding);
        } else {
          padding = mobile ?? const EdgeInsets.all(AppSpacing.screenPaddingHorizontal);
        }

        return Padding(
          padding: padding,
          child: child,
        );
      },
    );
  }
}

/// Grid responsif untuk menampilkan item dalam grid
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double aspectRatio;
  final double spacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.aspectRatio = 1.0,
    this.spacing = AppSpacing.md,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        
        int columns;
        if (AppBreakpoints.isDesktop(screenWidth)) {
          columns = 4;
        } else if (AppBreakpoints.isTablet(screenWidth)) {
          columns = 3;
        } else {
          columns = 2;
        }

        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: aspectRatio,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}

/// Komponen header responsif
class ResponsiveHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;

  const ResponsiveHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.actions,
    this.leading,
    this.centerTitle = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isDesktop = AppBreakpoints.isDesktop(screenWidth);
        final isTablet = AppBreakpoints.isTablet(screenWidth);

        return Container(
          padding: EdgeInsets.all(
            isDesktop ? AppSpacing.desktopPadding : 
            isTablet ? AppSpacing.tabletPadding : 
            AppSpacing.screenPaddingHorizontal,
          ),
          child: Column(
            crossAxisAlignment: centerTitle ? CrossAxisAlignment.center : CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (leading != null) ...[
                    leading!,
                    const SizedBox(width: AppSpacing.md),
                  ],
                  Expanded(
                    child: Column(
                      crossAxisAlignment: centerTitle ? CrossAxisAlignment.center : CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: isDesktop ? AppTypography.h1 : 
                                isTablet ? AppTypography.h2 : 
                                AppTypography.h3,
                        ),
                        if (subtitle != null) ...[
                          const SizedBox(height: AppSpacing.xs),
                          Text(
                            subtitle!,
                            style: isDesktop ? AppTypography.h5 : 
                                  isTablet ? AppTypography.h6 : 
                                  AppTypography.bodyLarge,
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (actions != null) ...[
                    const SizedBox(width: AppSpacing.md),
                    ...actions!,
                  ],
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Komponen kartu responsif
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isDesktop = AppBreakpoints.isDesktop(screenWidth);
        final isTablet = AppBreakpoints.isTablet(screenWidth);

        return Card(
          margin: EdgeInsets.all(
            isDesktop ? AppSpacing.desktopMargin : 
            isTablet ? AppSpacing.tabletMargin : 
            AppSpacing.cardMargin,
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(AppRadius.card),
            child: Padding(
              padding: EdgeInsets.all(
                isDesktop ? AppSpacing.desktopPadding : 
                isTablet ? AppSpacing.tabletPadding : 
                AppSpacing.cardPadding,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title != null || leading != null || trailing != null)
                    Row(
                      children: [
                        if (leading != null) ...[
                          leading!,
                          const SizedBox(width: AppSpacing.md),
                        ],
                        if (title != null)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  title!,
                                  style: isDesktop ? AppTypography.h4 : 
                                        isTablet ? AppTypography.h5 : 
                                        AppTypography.h6,
                                ),
                                if (subtitle != null) ...[
                                  const SizedBox(height: AppSpacing.xs),
                                  Text(
                                    subtitle!,
                                    style: isDesktop ? AppTypography.bodyLarge : 
                                          isTablet ? AppTypography.bodyMedium : 
                                          AppTypography.bodySmall,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        if (trailing != null) ...[
                          const SizedBox(width: AppSpacing.md),
                          trailing!,
                        ],
                      ],
                    ),
                  if (title != null || leading != null || trailing != null)
                    const SizedBox(height: AppSpacing.md),
                  child,
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Komponen navigation responsif
class ResponsiveNavigation extends StatelessWidget {
  final List<NavigationItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;

  const ResponsiveNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isDesktop = AppBreakpoints.isDesktop(screenWidth);
        final isTablet = AppBreakpoints.isTablet(screenWidth);

        if (isDesktop || isTablet) {
          return _buildSideNavigation();
        } else {
          return _buildBottomNavigation();
        }
      },
    );
  }

  Widget _buildSideNavigation() {
    return Container(
      width: 280,
      decoration: const BoxDecoration(
        color: AppColors.backgroundSecondary,
        border: Border(
          right: BorderSide(
            color: AppColors.neutralGray200,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.lg),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = index == currentIndex;

                return Container(
                  margin: const EdgeInsets.only(bottom: AppSpacing.xs),
                  child: ListTile(
                    leading: Icon(
                      item.icon,
                      color: isSelected 
                          ? AppColors.primary 
                          : AppColors.neutralGray600,
                    ),
                    title: Text(
                      item.label,
                      style: AppTypography.navigationItem.copyWith(
                        color: isSelected 
                            ? AppColors.primary 
                            : AppColors.textPrimary,
                        fontWeight: isSelected 
                            ? AppTypography.semiBold 
                            : AppTypography.regular,
                      ),
                    ),
                    selected: isSelected,
                    selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.md),
                    ),
                    onTap: () => onTap(index),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: onTap,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.neutralGray500,
      items: items.map((item) => BottomNavigationBarItem(
        icon: Icon(item.icon),
        label: item.label,
      )).toList(),
    );
  }
}

/// Model untuk item navigation
class NavigationItem {
  final String label;
  final IconData icon;
  final String? route;

  const NavigationItem({
    required this.label,
    required this.icon,
    this.route,
  });
}

/// Komponen untuk adaptive container
class AdaptiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final bool centerContent;

  const AdaptiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.centerContent = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isDesktop = AppBreakpoints.isDesktop(screenWidth);
        
        if (isDesktop && maxWidth != null) {
          return Center(
            child: SizedBox(
              width: maxWidth,
              child: child,
            ),
          );
        }
        
        return centerContent ? Center(child: child) : child;
      },
    );
  }
}

/// Komponen untuk form responsif
class ResponsiveForm extends StatelessWidget {
  final List<Widget> children;
  final double maxWidth;
  final bool centerForm;

  const ResponsiveForm({
    super.key,
    required this.children,
    this.maxWidth = 600,
    this.centerForm = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isDesktop = AppBreakpoints.isDesktop(screenWidth);
        
        Widget form = Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: children,
        );

        if (isDesktop && centerForm) {
          form = Center(
            child: SizedBox(
              width: maxWidth,
              child: form,
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(
            isDesktop ? AppSpacing.desktopPadding :
            AppBreakpoints.isTablet(screenWidth) ? AppSpacing.tabletPadding :
            AppSpacing.screenPaddingHorizontal,
          ),
          child: form,
        );
      },
    );
  }
}
