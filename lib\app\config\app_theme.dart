import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';

/// Theme configuration untuk Aplikasi SOD-MBG
/// Menggunakan Material Design 3 dengan customization untuk kebutuhan dapur
class AppTheme {
  // Logger untuk debugging
  static final Logger _logger = Logger();

  // Private constructor
  AppTheme._();

  // ===== LIGHT THEME =====
  /// Light theme untuk penggunaan umum
  static ThemeData get lightTheme {
    _logger.d('Building light theme for SOD-MBG');
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // Color Scheme
      colorScheme: _lightColorScheme,
      
      // Typography
      textTheme: _textTheme,
      
      // App Bar Theme
      appBarTheme: _lightAppBarTheme,
      
      // Card Theme
      cardTheme: _cardTheme,
      
      // Elevated Button Theme
      elevatedButtonTheme: _elevatedButtonTheme,
      
      // Outlined Button Theme
      outlinedButtonTheme: _outlinedButtonTheme,
      
      // Text Button Theme
      textButtonTheme: _textButtonTheme,
      
      // Input Decoration Theme
      inputDecorationTheme: _inputDecorationTheme,
      
      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: _bottomNavigationBarTheme,
      
      // Navigation Rail Theme
      navigationRailTheme: _navigationRailTheme,
      
      // Drawer Theme
      drawerTheme: _drawerTheme,
      
      // Dialog Theme
      dialogTheme: _dialogTheme,
      
      // Bottom Sheet Theme
      bottomSheetTheme: _bottomSheetTheme,
      
      // Chip Theme
      chipTheme: _chipTheme,
      
      // Tab Bar Theme
      tabBarTheme: _tabBarTheme,
      
      // List Tile Theme
      listTileTheme: _listTileTheme,
      
      // Floating Action Button Theme
      floatingActionButtonTheme: _fabTheme,
      
      // Icon Theme
      iconTheme: _iconTheme,
      
      // Switch Theme
      switchTheme: _switchTheme,
      
      // Checkbox Theme
      checkboxTheme: _checkboxTheme,
      
      // Radio Theme
      radioTheme: _radioTheme,
      
      // Divider Theme
      dividerTheme: _dividerTheme,
    );
  }

  // ===== DARK THEME =====
  /// Dark theme untuk penggunaan malam atau low-light environment
  static ThemeData get darkTheme {
    _logger.d('Building dark theme for SOD-MBG');
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // Color Scheme
      colorScheme: _darkColorScheme,
      
      // Typography
      textTheme: _textTheme,
      
      // App Bar Theme
      appBarTheme: _darkAppBarTheme,
      
      // Card Theme
      cardTheme: _cardTheme,
      
      // Button Themes (same as light)
      elevatedButtonTheme: _elevatedButtonTheme,
      outlinedButtonTheme: _outlinedButtonTheme,
      textButtonTheme: _textButtonTheme,
      
      // Input Decoration Theme
      inputDecorationTheme: _inputDecorationTheme,
      
      // Navigation Themes
      bottomNavigationBarTheme: _bottomNavigationBarTheme,
      navigationRailTheme: _navigationRailTheme,
      drawerTheme: _drawerTheme,
      
      // Dialog and Sheet Themes
      dialogTheme: _dialogTheme,
      bottomSheetTheme: _bottomSheetTheme,
      
      // Other component themes
      chipTheme: _chipTheme,
      tabBarTheme: _tabBarTheme,
      listTileTheme: _listTileTheme,
      floatingActionButtonTheme: _fabTheme,
      iconTheme: _iconTheme,
      switchTheme: _switchTheme,
      checkboxTheme: _checkboxTheme,
      radioTheme: _radioTheme,
      dividerTheme: _dividerTheme,
    );
  }

  // ===== KITCHEN THEME =====
  /// Special theme untuk kitchen display dengan visibilitas tinggi
  static ThemeData get kitchenTheme {
    _logger.d('Building kitchen theme for SOD-MBG');
    
    return lightTheme.copyWith(
      // Larger text untuk kitchen display
      textTheme: _kitchenTextTheme,
      
      // Higher contrast colors
      colorScheme: _kitchenColorScheme,
      
      // Larger touch targets
      materialTapTargetSize: MaterialTapTargetSize.padded,
    );
  }

  // ===== COLOR SCHEMES =====
  static const ColorScheme _lightColorScheme = ColorScheme.light(
    primary: AppColors.primary,
    onPrimary: AppColors.textOnPrimary,
    primaryContainer: AppColors.primaryLight,
    onPrimaryContainer: AppColors.textPrimary,
    
    secondary: AppColors.secondary,
    onSecondary: AppColors.textOnPrimary,
    secondaryContainer: AppColors.secondaryLight,
    onSecondaryContainer: AppColors.textPrimary,
    
    tertiary: AppColors.infoBlue,
    onTertiary: AppColors.textOnPrimary,
    
    error: AppColors.errorRed,
    onError: AppColors.textOnPrimary,
    
    surface: AppColors.surfaceColor,
    onSurface: AppColors.textPrimary,
    surfaceContainerHighest: AppColors.backgroundSecondary,
    
    outline: AppColors.neutralGray300,
    outlineVariant: AppColors.neutralGray200,
  );

  static const ColorScheme _darkColorScheme = ColorScheme.dark(
    primary: AppColors.primaryLight,
    onPrimary: AppColors.textPrimary,
    primaryContainer: AppColors.primaryDark,
    onPrimaryContainer: AppColors.textOnPrimary,
    
    secondary: AppColors.secondaryLight,
    onSecondary: AppColors.textPrimary,
    secondaryContainer: AppColors.secondaryDark,
    onSecondaryContainer: AppColors.textOnPrimary,
    
    tertiary: AppColors.infoBlue,
    onTertiary: AppColors.textOnPrimary,
    
    error: AppColors.errorRed,
    onError: AppColors.textOnPrimary,
    
    surface: AppColors.neutralGray800,
    onSurface: AppColors.textOnPrimary,
    surfaceContainerHighest: AppColors.neutralGray700,
    
    outline: AppColors.neutralGray600,
    outlineVariant: AppColors.neutralGray700,
  );

  static const ColorScheme _kitchenColorScheme = ColorScheme.light(
    primary: AppColors.primaryDark,
    onPrimary: AppColors.textOnPrimary,
    primaryContainer: AppColors.primary,
    onPrimaryContainer: AppColors.textOnPrimary,
    
    secondary: AppColors.secondaryDark,
    onSecondary: AppColors.textOnPrimary,
    secondaryContainer: AppColors.secondary,
    onSecondaryContainer: AppColors.textOnPrimary,
    
    error: AppColors.errorRed,
    onError: AppColors.textOnPrimary,
    
    surface: AppColors.neutralWhite,
    onSurface: AppColors.textPrimary,
  );

  // ===== TEXT THEMES =====
  static final TextTheme _textTheme = TextTheme(
    displayLarge: AppTypography.h1.copyWith(color: AppColors.textPrimary),
    displayMedium: AppTypography.h2.copyWith(color: AppColors.textPrimary),
    displaySmall: AppTypography.h3.copyWith(color: AppColors.textPrimary),
    
    headlineLarge: AppTypography.h4.copyWith(color: AppColors.textPrimary),
    headlineMedium: AppTypography.h5.copyWith(color: AppColors.textPrimary),
    headlineSmall: AppTypography.h6.copyWith(color: AppColors.textPrimary),
    
    titleLarge: AppTypography.h6.copyWith(color: AppColors.textPrimary),
    titleMedium: AppTypography.labelLarge.copyWith(color: AppColors.textPrimary),
    titleSmall: AppTypography.labelMedium.copyWith(color: AppColors.textPrimary),
    
    bodyLarge: AppTypography.bodyLarge.copyWith(color: AppColors.textPrimary),
    bodyMedium: AppTypography.bodyMedium.copyWith(color: AppColors.textPrimary),
    bodySmall: AppTypography.bodySmall.copyWith(color: AppColors.textSecondary),
    
    labelLarge: AppTypography.labelLarge.copyWith(color: AppColors.textPrimary),
    labelMedium: AppTypography.labelMedium.copyWith(color: AppColors.textSecondary),
    labelSmall: AppTypography.labelSmall.copyWith(color: AppColors.textTertiary),
  );

  static final TextTheme _kitchenTextTheme = TextTheme(
    displayLarge: AppTypography.kitchenDisplay.copyWith(color: AppColors.textPrimary),
    displayMedium: AppTypography.kitchenDisplay.copyWith(
      fontSize: 32, 
      color: AppColors.textPrimary,
    ),
    displaySmall: AppTypography.kitchenStatus.copyWith(color: AppColors.textPrimary),
    
    headlineLarge: AppTypography.kitchenCounter.copyWith(color: AppColors.textPrimary),
    headlineMedium: AppTypography.kitchenStatus.copyWith(color: AppColors.textPrimary),
    headlineSmall: AppTypography.h6.copyWith(fontSize: 24, color: AppColors.textPrimary),
    
    bodyLarge: AppTypography.bodyLarge.copyWith(fontSize: 18, color: AppColors.textPrimary),
    bodyMedium: AppTypography.bodyMedium.copyWith(fontSize: 16, color: AppColors.textPrimary),
    bodySmall: AppTypography.bodySmall.copyWith(fontSize: 14, color: AppColors.textSecondary),
    
    labelLarge: AppTypography.labelLarge.copyWith(fontSize: 16, color: AppColors.textPrimary),
    labelMedium: AppTypography.labelMedium.copyWith(fontSize: 14, color: AppColors.textSecondary),
    labelSmall: AppTypography.labelSmall.copyWith(fontSize: 12, color: AppColors.textTertiary),
  );

  // ===== APP BAR THEMES =====
  static const AppBarTheme _lightAppBarTheme = AppBarTheme(
    elevation: AppElevation.appBar,
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textOnPrimary,
    titleTextStyle: TextStyle(
      fontFamily: AppTypography.primaryFontFamily,
      fontSize: AppTypography.fontSize20,
      fontWeight: AppTypography.semiBold,
      color: AppColors.textOnPrimary,
    ),
    iconTheme: IconThemeData(
      color: AppColors.textOnPrimary,
      size: 24,
    ),
  );

  static const AppBarTheme _darkAppBarTheme = AppBarTheme(
    elevation: AppElevation.appBar,
    backgroundColor: AppColors.neutralGray800,
    foregroundColor: AppColors.textOnPrimary,
    titleTextStyle: TextStyle(
      fontFamily: AppTypography.primaryFontFamily,
      fontSize: AppTypography.fontSize20,
      fontWeight: AppTypography.semiBold,
      color: AppColors.textOnPrimary,
    ),
    iconTheme: IconThemeData(
      color: AppColors.textOnPrimary,
      size: 24,
    ),
  );

  // ===== CARD THEME =====
  static const CardThemeData _cardTheme = CardThemeData(
    elevation: AppElevation.card,
    margin: EdgeInsets.all(AppSpacing.cardMargin),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.card)),
    ),
  );

  // ===== BUTTON THEMES =====
  static final ElevatedButtonThemeData _elevatedButtonTheme = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: AppElevation.button,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.buttonPaddingHorizontal,
        vertical: AppSpacing.buttonPaddingVertical,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(AppRadius.button)),
      ),
      textStyle: AppTypography.buttonMedium,
    ),
  );

  static final OutlinedButtonThemeData _outlinedButtonTheme = OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.buttonPaddingHorizontal,
        vertical: AppSpacing.buttonPaddingVertical,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(AppRadius.button)),
      ),
      textStyle: AppTypography.buttonMedium,
    ),
  );

  static final TextButtonThemeData _textButtonTheme = TextButtonThemeData(
    style: TextButton.styleFrom(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.buttonPaddingHorizontal,
        vertical: AppSpacing.buttonPaddingVertical,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(AppRadius.button)),
      ),
      textStyle: AppTypography.buttonMedium,
    ),
  );

  // ===== INPUT DECORATION THEME =====
  static const InputDecorationTheme _inputDecorationTheme = InputDecorationTheme(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.input)),
    ),
    contentPadding: EdgeInsets.symmetric(
      horizontal: AppSpacing.md,
      vertical: AppSpacing.sm,
    ),
    filled: true,
    fillColor: AppColors.backgroundSecondary,
  );

  // ===== NAVIGATION THEMES =====
  static const BottomNavigationBarThemeData _bottomNavigationBarTheme = BottomNavigationBarThemeData(
    elevation: AppElevation.appBar,
    selectedItemColor: AppColors.primary,
    unselectedItemColor: AppColors.neutralGray500,
    selectedLabelStyle: AppTypography.labelSmall,
    unselectedLabelStyle: AppTypography.labelSmall,
  );

  static const NavigationRailThemeData _navigationRailTheme = NavigationRailThemeData(
    elevation: AppElevation.appBar,
    selectedIconTheme: IconThemeData(color: AppColors.primary),
    unselectedIconTheme: IconThemeData(color: AppColors.neutralGray500),
    selectedLabelTextStyle: AppTypography.labelSmall,
    unselectedLabelTextStyle: AppTypography.labelSmall,
  );

  static const DrawerThemeData _drawerTheme = DrawerThemeData(
    elevation: AppElevation.dialog,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topRight: Radius.circular(AppRadius.dialog),
        bottomRight: Radius.circular(AppRadius.dialog),
      ),
    ),
  );

  // ===== DIALOG THEMES =====
  static const DialogThemeData _dialogTheme = DialogThemeData(
    elevation: AppElevation.dialog,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.dialog)),
    ),
    titleTextStyle: TextStyle(
      fontFamily: AppTypography.primaryFontFamily,
      fontSize: AppTypography.fontSize20,
      fontWeight: AppTypography.semiBold,
      color: AppColors.textPrimary,
    ),
    contentTextStyle: AppTypography.bodyMedium,
  );

  static const BottomSheetThemeData _bottomSheetTheme = BottomSheetThemeData(
    elevation: AppElevation.bottomSheet,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(AppRadius.bottomSheet),
        topRight: Radius.circular(AppRadius.bottomSheet),
      ),
    ),
  );

  // ===== OTHER COMPONENT THEMES =====
  static const ChipThemeData _chipTheme = ChipThemeData(
    elevation: AppElevation.level1,
    side: BorderSide.none,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.xl)),
    ),
    labelStyle: AppTypography.labelMedium,
  );

  static const TabBarThemeData _tabBarTheme = TabBarThemeData(
    labelStyle: AppTypography.tabItem,
    unselectedLabelStyle: AppTypography.tabItem,
    labelColor: AppColors.primary,
    unselectedLabelColor: AppColors.neutralGray500,
  );

  static const ListTileThemeData _listTileTheme = ListTileThemeData(
    contentPadding: EdgeInsets.symmetric(
      horizontal: AppSpacing.listItemPadding,
      vertical: AppSpacing.listItemSpacing,
    ),
    titleTextStyle: AppTypography.bodyMedium,
    subtitleTextStyle: AppTypography.bodySmall,
  );

  static const FloatingActionButtonThemeData _fabTheme = FloatingActionButtonThemeData(
    elevation: AppElevation.fab,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.xl)),
    ),
  );

  static const IconThemeData _iconTheme = IconThemeData(
    size: 24,
    color: AppColors.neutralGray700,
  );

  static final SwitchThemeData _switchTheme = SwitchThemeData(
    thumbColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary;
      }
      return AppColors.neutralGray400;
    }),
    trackColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primaryLight;
      }
      return AppColors.neutralGray300;
    }),
  );

  static final CheckboxThemeData _checkboxTheme = CheckboxThemeData(
    fillColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary;
      }
      return AppColors.neutralGray300;
    }),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.sm)),
    ),
  );

  static final RadioThemeData _radioTheme = RadioThemeData(
    fillColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary;
      }
      return AppColors.neutralGray300;
    }),
  );

  static const DividerThemeData _dividerTheme = DividerThemeData(
    color: AppColors.neutralGray200,
    thickness: 1,
    space: AppSpacing.md,
  );
}
