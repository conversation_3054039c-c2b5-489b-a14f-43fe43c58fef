import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:aplikasi_sppg/core/auth/presentation/cubit/auth_cubit.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/dashboard_repository.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/dashboard_summary.dart';

part 'dashboard_state.dart';

class DashboardCubit extends Cubit<DashboardState> {
  final DashboardRepository _dashboardRepository;
  final AuthCubit _authCubit;

  DashboardCubit(this._dashboardRepository, this._authCubit)
      : super(DashboardInitial());

  Future<void> getDashboardSummary() async {
    final authState = _authCubit.state;
    if (authState is Authenticated) {
      emit(DashboardLoading());
      try {
        final summary =
            await _dashboardRepository.getDashboardSummary(authState.user.role);
        emit(DashboardLoaded(summary));
      } catch (e) {
        emit(DashboardError(e.toString()));
      }
    }
  }
}
