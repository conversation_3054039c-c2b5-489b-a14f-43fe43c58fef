import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';

/// Custom card component untuk Aplikasi SOD-MBG
/// Menyediakan berbagai varian card untuk kebutuhan yang berbeda
class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? backgroundColor;
  final double? elevation;
  final VoidCallback? onTap;
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final bool showDivider;
  final AppCardType type;

  static final Logger _logger = Logger();

  const AppCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.onTap,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.showDivider = false,
    this.type = AppCardType.basic,
  });

  @override
  Widget build(BuildContext context) {
    _logger.d('Building AppCard: type: $type, title: $title');

    final theme = Theme.of(context);

    Widget cardContent = _buildCardContent(context);

    return Container(
      margin: margin ?? const EdgeInsets.all(AppSpacing.cardMargin),
      child: Material(
        color: backgroundColor ?? theme.cardColor,
        elevation: elevation ?? AppElevation.card,
        borderRadius: BorderRadius.circular(_getRadius()),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(_getRadius()),
          child: Container(
            padding: padding ?? _getDefaultPadding(),
            child: cardContent,
          ),
        ),
      ),
    );
  }

  Widget _buildCardContent(BuildContext context) {
    switch (type) {
      case AppCardType.basic:
        return _buildBasicCard();
      case AppCardType.header:
        return _buildHeaderCard();
      case AppCardType.status:
        return _buildStatusCard(context);
      case AppCardType.kitchen:
        return _buildKitchenCard(context);
      case AppCardType.metric:
        return _buildMetricCard(context);
    }
  }

  Widget _buildBasicCard() {
    return child;
  }

  Widget _buildHeaderCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null || leading != null || trailing != null)
          _buildHeader(),
        if (showDivider && (title != null || leading != null || trailing != null))
          const Divider(),
        child,
      ],
    );
  }

  Widget _buildStatusCard(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        if (showDivider)
          const Divider(),
        child,
      ],
    );
  }

  Widget _buildKitchenCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_getRadius()),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null || leading != null || trailing != null)
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppRadius.card),
                  topRight: Radius.circular(AppRadius.card),
                ),
              ),
              child: _buildHeader(),
            ),
          Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: child,
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_getRadius()),
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primaryLight.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null || leading != null || trailing != null)
            _buildHeader(),
          if (showDivider)
            const Divider(),
          child,
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        if (leading != null) ...[
          leading!,
          const SizedBox(width: AppSpacing.sm),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (title != null)
                Text(
                  title!,
                  style: AppTypography.h6,
                ),
              if (subtitle != null)
                Text(
                  subtitle!,
                  style: AppTypography.bodySmall,
                ),
            ],
          ),
        ),
        if (trailing != null) ...[
          const SizedBox(width: AppSpacing.sm),
          trailing!,
        ],
      ],
    );
  }

  double _getRadius() {
    switch (type) {
      case AppCardType.basic:
      case AppCardType.header:
      case AppCardType.status:
      case AppCardType.metric:
        return AppRadius.card;
      case AppCardType.kitchen:
        return AppRadius.kitchenCard;
    }
  }

  EdgeInsets _getDefaultPadding() {
    switch (type) {
      case AppCardType.basic:
      case AppCardType.header:
      case AppCardType.status:
      case AppCardType.metric:
        return const EdgeInsets.all(AppSpacing.cardPadding);
      case AppCardType.kitchen:
        return EdgeInsets.zero; // Kitchen card has custom padding
    }
  }
}

enum AppCardType {
  basic,
  header,
  status,
  kitchen,
  metric,
}

/// Factory constructors untuk kemudahan penggunaan
class AppCardFactory {
  // Private constructor
  AppCardFactory._();

  /// Basic card
  static AppCard basic({
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? backgroundColor,
    double? elevation,
    VoidCallback? onTap,
  }) {
    return AppCard(
      type: AppCardType.basic,
      padding: padding,
      margin: margin,
      backgroundColor: backgroundColor,
      elevation: elevation,
      onTap: onTap,
      child: child,
    );
  }

  /// Header card with title and optional subtitle
  static AppCard header({
    required Widget child,
    required String title,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    bool showDivider = true,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? backgroundColor,
    double? elevation,
    VoidCallback? onTap,
  }) {
    return AppCard(
      type: AppCardType.header,
      title: title,
      subtitle: subtitle,
      leading: leading,
      trailing: trailing,
      showDivider: showDivider,
      padding: padding,
      margin: margin,
      backgroundColor: backgroundColor,
      elevation: elevation,
      onTap: onTap,
      child: child,
    );
  }

  /// Status card dengan indikator status
  static AppCard status({
    required Widget child,
    required String title,
    String? subtitle,
    required AppStatusType status,
    Widget? trailing,
    bool showDivider = false,
    EdgeInsets? padding,
    EdgeInsets? margin,
    VoidCallback? onTap,
  }) {
    return AppCard(
      type: AppCardType.status,
      title: title,
      subtitle: subtitle,
      leading: Icon(
        AppIcons.getStatusIcon(status.name),
        color: _getStatusColor(status),
        size: AppIcons.sizeMedium,
      ),
      trailing: trailing,
      showDivider: showDivider,
      padding: padding,
      margin: margin,
      onTap: onTap,
      child: child,
    );
  }

  /// Kitchen operation card
  static AppCard kitchen({
    required Widget child,
    required String title,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    EdgeInsets? margin,
    VoidCallback? onTap,
  }) {
    return AppCard(
      type: AppCardType.kitchen,
      title: title,
      subtitle: subtitle,
      leading: leading,
      trailing: trailing,
      margin: margin,
      onTap: onTap,
      child: child,
    );
  }

  /// Metric card untuk menampilkan statistik
  static AppCard metric({
    required Widget child,
    required String title,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    bool showDivider = false,
    EdgeInsets? padding,
    EdgeInsets? margin,
    VoidCallback? onTap,
  }) {
    return AppCard(
      type: AppCardType.metric,
      title: title,
      subtitle: subtitle,
      leading: leading,
      trailing: trailing,
      showDivider: showDivider,
      padding: padding,
      margin: margin,
      onTap: onTap,
      child: child,
    );
  }

  static Color _getStatusColor(AppStatusType status) {
    switch (status) {
      case AppStatusType.success:
        return AppColors.successGreen;
      case AppStatusType.warning:
        return AppColors.warningYellow;
      case AppStatusType.error:
        return AppColors.errorRed;
      case AppStatusType.info:
        return AppColors.infoBlue;
      case AppStatusType.pending:
        return AppColors.neutralGray500;
    }
  }
}

enum AppStatusType {
  success,
  warning,
  error,
  info,
  pending,
}
