import 'package:flutter/material.dart';

/// Icons system untuk Aplikasi SOD-MBG
/// Menggunakan Material Icons dengan tambahan custom icons untuk operasional dapur
class AppIcons {
  // Private constructor
  AppIcons._();

  // ===== NAVIGATION ICONS =====
  static const IconData home = Icons.home;
  static const IconData dashboard = Icons.dashboard;
  static const IconData menu = Icons.menu;
  static const IconData back = Icons.arrow_back;
  static const IconData close = Icons.close;
  static const IconData search = Icons.search;
  static const IconData filter = Icons.filter_list;
  static const IconData sort = Icons.sort;
  static const IconData refresh = Icons.refresh;

  // ===== USER & AUTH ICONS =====
  static const IconData user = Icons.person;
  static const IconData userGroup = Icons.group;
  static const IconData login = Icons.login;
  static const IconData logout = Icons.logout;
  static const IconData profile = Icons.account_circle;
  static const IconData settings = Icons.settings;
  static const IconData security = Icons.security;

  // ===== KITCHEN OPERATION ICONS =====
  static const IconData kitchen = Icons.kitchen;
  static const IconData cooking = Icons.outdoor_grill;
  static const IconData restaurant = Icons.restaurant;
  static const IconData localDining = Icons.local_dining;
  static const IconData chefHat = Icons.cookie; // Placeholder untuk chef hat
  static const IconData recipe = Icons.receipt_long;
  static const IconData menuBook = Icons.menu_book;
  static const IconData timer = Icons.timer;
  static const IconData temperature = Icons.thermostat;
  static const IconData scale = Icons.monitor_weight;

  // ===== INVENTORY ICONS =====
  static const IconData inventory = Icons.inventory_2;
  static const IconData storage = Icons.storage;
  static const IconData warehouse = Icons.warehouse;
  static const IconData boxes = Icons.inventory;
  static const IconData scanning = Icons.qr_code_scanner;
  static const IconData barcode = Icons.qr_code;
  static const IconData stockIn = Icons.add_box;
  static const IconData stockOut = Icons.remove_from_queue;
  static const IconData expiry = Icons.schedule;

  // ===== QUALITY CONTROL ICONS =====
  static const IconData qualityControl = Icons.verified;
  static const IconData checkmark = Icons.check_circle;
  static const IconData warning = Icons.warning;
  static const IconData error = Icons.error;
  static const IconData pending = Icons.pending;
  static const IconData inspection = Icons.fact_check;
  static const IconData approval = Icons.thumb_up;
  static const IconData rejection = Icons.thumb_down;

  // ===== LOGISTICS & DISTRIBUTION ICONS =====
  static const IconData delivery = Icons.local_shipping;
  static const IconData truck = Icons.fire_truck;
  static const IconData route = Icons.route;
  static const IconData gps = Icons.gps_fixed;
  static const IconData location = Icons.location_on;
  static const IconData tracking = Icons.track_changes;
  static const IconData package = Icons.local_post_office;
  static const IconData fleet = Icons.directions_car;

  // ===== FINANCIAL ICONS =====
  static const IconData money = Icons.attach_money;
  static const IconData payment = Icons.payment;
  static const IconData receipt = Icons.receipt;
  static const IconData invoice = Icons.description;
  static const IconData budget = Icons.account_balance_wallet;
  static const IconData transaction = Icons.swap_horiz;
  static const IconData report = Icons.assessment;
  static const IconData analytics = Icons.analytics;

  // ===== STATUS ICONS =====
  static const IconData statusActive = Icons.radio_button_checked;
  static const IconData statusInactive = Icons.radio_button_unchecked;
  static const IconData statusSuccess = Icons.check_circle;
  static const IconData statusWarning = Icons.warning_amber;
  static const IconData statusError = Icons.error_outline;
  static const IconData statusInfo = Icons.info_outline;
  static const IconData statusPending = Icons.hourglass_empty;

  // ===== ACTION ICONS =====
  static const IconData add = Icons.add;
  static const IconData edit = Icons.edit;
  static const IconData delete = Icons.delete;
  static const IconData save = Icons.save;
  static const IconData cancel = Icons.cancel;
  static const IconData copy = Icons.content_copy;
  static const IconData share = Icons.share;
  static const IconData download = Icons.download;
  static const IconData upload = Icons.upload;
  static const IconData export = Icons.file_download;
  static const IconData import = Icons.file_upload;

  // ===== FORM ICONS =====
  static const IconData calendar = Icons.calendar_today;
  static const IconData time = Icons.access_time;
  static const IconData date = Icons.date_range;
  static const IconData dropdown = Icons.arrow_drop_down;
  static const IconData checkbox = Icons.check_box;
  static const IconData radioButton = Icons.radio_button_checked;
  static const IconData textField = Icons.text_fields;
  static const IconData camera = Icons.camera_alt;
  static const IconData gallery = Icons.photo_library;

  // ===== COMMUNICATION ICONS =====
  static const IconData notification = Icons.notifications;
  static const IconData message = Icons.message;
  static const IconData email = Icons.email;
  static const IconData phone = Icons.phone;
  static const IconData chat = Icons.chat;
  static const IconData announcement = Icons.campaign;

  // ===== UTILITY ICONS =====
  static const IconData more = Icons.more_vert;
  static const IconData moreHorizontal = Icons.more_horiz;
  static const IconData expand = Icons.expand_more;
  static const IconData collapse = Icons.expand_less;
  static const IconData visibility = Icons.visibility;
  static const IconData visibilityOff = Icons.visibility_off;
  static const IconData help = Icons.help_outline;
  static const IconData info = Icons.info_outline;

  // ===== ROLE SPECIFIC ICONS =====
  /// Admin Yayasan
  static const IconData adminYayasan = Icons.admin_panel_settings;
  
  /// Perwakilan Yayasan
  static const IconData perwakilanYayasan = Icons.supervisor_account;
  
  /// Kepala Dapur
  static const IconData kepalaDapur = Icons.restaurant_menu;
  
  /// Ahli Gizi
  static const IconData ahliGizi = Icons.health_and_safety;
  
  /// Akuntan
  static const IconData akuntan = Icons.calculate;
  
  /// Pengawas Pemeliharaan
  static const IconData pengawasPemeliharaan = Icons.engineering;

  // ===== KITCHEN AREA ICONS =====
  static const IconData preparationArea = Icons.content_cut;
  static const IconData cookingArea = Icons.local_fire_department;
  static const IconData storageArea = Icons.storage;
  static const IconData cleaningArea = Icons.cleaning_services;
  static const IconData servingArea = Icons.room_service;
  static const IconData washingArea = Icons.local_laundry_service;

  // ===== SAFETY ICONS =====
  static const IconData safety = Icons.health_and_safety;
  static const IconData hazard = Icons.dangerous;
  static const IconData firstAid = Icons.local_hospital;
  static const IconData fire = Icons.local_fire_department;
  static const IconData emergency = Icons.emergency;

  // ===== CONNECTIVITY ICONS =====
  static const IconData online = Icons.wifi;
  static const IconData offline = Icons.wifi_off;
  static const IconData sync = Icons.sync;
  static const IconData syncProblem = Icons.sync_problem;
  static const IconData cloud = Icons.cloud;
  static const IconData cloudOff = Icons.cloud_off;

  // ===== SIZE VARIANTS =====
  /// Icon size untuk button kecil
  static const double sizeSmall = 16.0;
  
  /// Icon size standar
  static const double sizeMedium = 24.0;
  
  /// Icon size untuk button besar
  static const double sizeLarge = 32.0;
  
  /// Icon size untuk display
  static const double sizeXLarge = 48.0;
  
  /// Icon size untuk kitchen display
  static const double sizeKitchen = 64.0;

  // ===== UTILITY METHODS =====
  /// Mendapatkan icon berdasarkan role
  static IconData getRoleIcon(String role) {
    switch (role.toLowerCase()) {
      case 'admin_yayasan':
        return adminYayasan;
      case 'perwakilan_yayasan':
        return perwakilanYayasan;
      case 'kepala_dapur':
        return kepalaDapur;
      case 'ahli_gizi':
        return ahliGizi;
      case 'akuntan':
        return akuntan;
      case 'pengawas_pemeliharaan':
        return pengawasPemeliharaan;
      default:
        return user;
    }
  }

  /// Mendapatkan icon berdasarkan status
  static IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'approved':
        return statusSuccess;
      case 'warning':
      case 'pending':
        return statusWarning;
      case 'error':
      case 'failed':
      case 'rejected':
        return statusError;
      case 'info':
        return statusInfo;
      case 'active':
        return statusActive;
      case 'inactive':
        return statusInactive;
      default:
        return statusPending;
    }
  }

  /// Mendapatkan icon berdasarkan area dapur
  static IconData getKitchenAreaIcon(String area) {
    switch (area.toLowerCase()) {
      case 'preparation':
        return preparationArea;
      case 'cooking':
        return cookingArea;
      case 'storage':
        return storageArea;
      case 'cleaning':
        return cleaningArea;
      case 'serving':
        return servingArea;
      case 'washing':
        return washingArea;
      default:
        return kitchen;
    }
  }

  // ===== LEGACY ALIASES FOR BACKWARD COMPATIBILITY =====
  /// Alias untuk financial operations
  static const IconData financial = money;
  
  /// Alias untuk logistics operations
  static const IconData logistics = delivery;
}
