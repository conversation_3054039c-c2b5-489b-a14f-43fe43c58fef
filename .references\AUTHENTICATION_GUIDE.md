# Authentication Implementation Guide

This document provides a comprehensive guide for the Supabase authentication implementation in the SOD-MBG application.

## Overview

The authentication system is built using Supabase as the backend with a robust, offline-first architecture. It supports both regular user authentication and anonymous guest access.

## Architecture

### Core Components

1. **AuthService** - Main service for authentication operations
2. **AuthRepository** - Interface for authentication data operations
3. **SupabaseAuthRepository** - Supabase implementation of AuthRepository
4. **AuthState** - State management for authentication
5. **AuthBuilder** - Widget for reactive authentication UI
6. **AuthMixin** - Mixin for easy auth integration in widgets

### Key Features

- **Email/Password Authentication**: Standard user login
- **Anonymous Authentication**: Guest access without registration
- **Offline Support**: Cached authentication state
- **Auto Token Refresh**: Automatic session management
- **Role-Based Access Control**: Support for 6 different user roles
- **Session Management**: Secure session handling with automatic cleanup

## User Roles

1. **<PERSON><PERSON>** - Full system access
2. **<PERSON><PERSON><PERSON><PERSON>** - Operational supervisor
3. **<PERSON><PERSON><PERSON> SPPG** - Kitchen manager
4. **Ah<PERSON>i** - Nutritionist
5. **Akuntan** - Accountant
6. **Pengawas Pemeliharaan & Penghantaran** - Logistics supervisor

## Authentication Flow

### 1. Email/Password Login

```dart
final result = await authService.signInWithEmail(
  email: email,
  password: password,
);

if (result is AuthenticatedState) {
  // Login successful
  final user = result.user;
} else if (result is AuthErrorState) {
  // Handle error
  print('Login failed: ${result.error}');
}
```

### 2. Anonymous Login

```dart
final result = await authService.signInAnonymously();

if (result is AnonymousState) {
  // Anonymous login successful
  final user = result.user;
} else if (result is AuthErrorState) {
  // Handle error
  print('Anonymous login failed: ${result.error}');
}
```

### 3. Sign Out

```dart
final result = await authService.signOut();

if (result is UnauthenticatedState) {
  // Sign out successful
}
```

## Widget Integration

### Using AuthBuilder

```dart
AuthBuilder(
  builder: (context, authState) {
    if (authState is AuthenticatedState) {
      return DashboardPage();
    } else if (authState is AnonymousState) {
      return GuestDashboardPage();
    } else if (authState is UnauthenticatedState) {
      return LoginPage();
    } else if (authState is AuthLoadingState) {
      return LoadingPage();
    } else if (authState is AuthErrorState) {
      return ErrorPage(error: authState.error);
    }
    return LoginPage();
  },
)
```

### Using AuthMixin

```dart
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> with AuthMixin<MyWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('User: ${currentUser?.displayName ?? 'Not logged in'}'),
        Text('Role: ${currentUser?.roleDisplayName ?? 'Unknown'}'),
        ElevatedButton(
          onPressed: isLoggedIn ? _handleSignOut : _handleSignIn,
          child: Text(isLoggedIn ? 'Sign Out' : 'Sign In'),
        ),
      ],
    );
  }

  @override
  void onUserSignedIn(user) {
    print('User signed in: ${user.displayName}');
  }

  @override
  void onUserSignedOut() {
    print('User signed out');
  }
}
```

## Configuration

### Environment Variables

Create a `.env` file in your project root:

```env
# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### Supabase Setup

1. Create a Supabase project
2. Enable Email authentication
3. Enable Anonymous sign-ins (optional)
4. Set up RLS policies for user data
5. Create a `users` table with the following schema:

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT,
    nama TEXT,
    role TEXT NOT NULL DEFAULT 'guest',
    sppg_id TEXT,
    sppg_name TEXT,
    is_anonymous BOOLEAN DEFAULT false,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Testing

### Auth Integration Demo

The application includes a comprehensive auth integration demo at `/auth-integration`. This demo shows:

- Current authentication state
- User information display
- Authentication actions (login, logout, guest access)
- Debug information
- Real-time state updates

To access the demo:

```dart
AppRouter.goToAuthIntegration(context);
```

### Running Tests

```bash
# Run all tests
flutter test

# Run auth-specific tests
flutter test test/auth/

# Run integration tests
flutter test integration_test/
```

## Authentication States

The authentication system uses the following states:

- **AuthInitialState** - Initial state before authentication check
- **AuthLoadingState** - Loading state during authentication operations
- **AuthenticatedState** - User successfully authenticated
- **AnonymousState** - User signed in anonymously
- **UnauthenticatedState** - User not authenticated
- **AuthErrorState** - Authentication error occurred
- **SessionExpiredState** - User session expired
- **LoggingOutState** - User signing out

## Security Considerations

1. **Row Level Security (RLS)**: Enabled on all user tables
2. **Token Security**: Secure token storage using FlutterSecureStorage
3. **Session Management**: Automatic token refresh with configurable thresholds
4. **Role-Based Access**: Built-in role validation and feature access control
5. **Offline Security**: Cached credentials are encrypted

## Troubleshooting

### Common Issues

1. **Authentication not working**: Check Supabase configuration and network connectivity
2. **Anonymous login fails**: Verify anonymous authentication is enabled in Supabase
3. **Session expires quickly**: Adjust session timeout in configuration
4. **Role permissions**: Ensure user roles are properly set in the database

### Debug Mode

Enable debug logging for authentication:

```dart
// Set environment variable
const String.fromEnvironment('ENABLE_LOGGING', defaultValue: 'true')

// Or in code
Logger.level = Level.debug;
```

## Future Enhancements

- [ ] Biometric authentication support
- [ ] Multi-factor authentication (MFA)
- [ ] Social login providers (Google, Facebook, etc.)
- [ ] Advanced role permissions system
- [ ] Audit logging for authentication events
- [ ] Password complexity requirements
- [ ] Account lockout after failed attempts

## Support

For issues or questions regarding authentication:

1. Check the auth integration demo for examples
2. Review the troubleshooting section
3. Check Supabase documentation
4. Create an issue in the project repository

---

**Note**: This implementation is designed for the SOD-MBG kitchen management system and includes specific role-based access controls and offline capabilities required for kitchen operations.
