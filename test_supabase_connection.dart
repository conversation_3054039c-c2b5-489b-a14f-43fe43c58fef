import 'package:aplikasi_sppg/core/config/supabase_config.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  final logger = Logger();
  
  try {
    logger.i('Testing Supabase configuration...');
    
    // Test configuration values
    logger.i('Supabase URL: ${SupabaseConfig.supabaseUrl}');
    logger.i('Anon Key length: ${SupabaseConfig.supabaseAnonKey.length}');
    logger.i('Service Role Key length: ${SupabaseConfig.supabaseServiceRoleKey.length}');
    
    // Initialize Supabase
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
    
    logger.i('Supabase initialization successful!');
    
    // Test basic connectivity
    final client = Supabase.instance.client;
    logger.i('Supabase client initialized successfully');
    
    // Test if we can read from the database
    final response = await client.from('roles').select('*').limit(1);
    logger.i('Database query successful: ${response.length} records');
    
    logger.i('All tests passed! Supabase configuration is working correctly.');
    
  } catch (e, stackTrace) {
    logger.e('Supabase configuration test failed: $e');
    logger.e('Stack trace: $stackTrace');
  }
}
