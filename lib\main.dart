import 'package:flutter/material.dart';
import 'package:desktop_window/desktop_window.dart';
import 'package:logger/logger.dart';
import 'app/config/app_theme.dart';
import 'app/config/app_router.dart';
import 'core/config/supabase_config.dart';
import 'core/config/supabase_service.dart';
import 'core/auth/presentation/auth_service.dart';

void main() async {
  final logger = Logger();
  
  WidgetsFlutterBinding.ensureInitialized();
  
  // Configure desktop window untuk fullscreen
  try {
    await DesktopWindow.setFullScreen(true);
    logger.i('Desktop window configured for fullscreen');
  } catch (e) {
    logger.w('Failed to configure desktop window: $e');
  }
  
  // Initialize Supabase
  bool supabaseInitialized = false;
  try {
    logger.i('Initializing environment configuration...');
    await SupabaseConfig.initialize();
    
    logger.i('Initializing Supabase...');
    final supabaseService = SupabaseService.instance;
    supabaseInitialized = await supabaseService.initialize();
    
    if (supabaseInitialized) {
      logger.i('Supabase initialized successfully');
    } else {
      logger.w('Supabase initialization failed - will use mock authentication');
    }
  } catch (e) {
    logger.e('Failed to initialize Supabase: $e');
    logger.w('Will use mock authentication instead');
    supabaseInitialized = false;
  }
  
  // Initialize AuthService (will use mock if Supabase is not available)
  try {
    logger.i('Initializing AuthService...');
    final authService = AuthService.instance;
    await authService.initialize();
    
    if (authService.isMockMode) {
      logger.w('AuthService initialized in mock mode');
    } else {
      logger.i('AuthService initialized with Supabase');
    }
  } catch (e) {
    logger.e('Failed to initialize AuthService: $e');
    logger.w('Authentication features will be limited');
  }
  
  logger.i('Starting SOD-MBG Application');
  runApp(const SodMbgApp());
}

class SodMbgApp extends StatelessWidget {
  const SodMbgApp({super.key});

  @override
  Widget build(BuildContext context) {
    final logger = Logger();
    logger.d('Building SodMbgApp');

    return MaterialApp.router(
      title: 'SOD-MBG - Sistem Operasional Dapur MBG',
      debugShowCheckedModeBanner: false,
      
      // Theme configuration
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      
      // Router configuration
      routerConfig: AppRouter.router,
      
      // Builder untuk responsive handling
      builder: (context, child) {
        // Ensure minimum text scale factor untuk kitchen displays
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0),
          ),
          child: child!,
        );
      },
    );
  }
}
