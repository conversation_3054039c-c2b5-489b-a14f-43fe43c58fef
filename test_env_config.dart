import 'lib/core/config/supabase_config.dart';

void main() async {
  print('Testing Environment Configuration...');
  
  // Initialize the configuration
  await SupabaseConfig.initialize();
  
  // Test the configuration values
  print('Supabase URL: ${SupabaseConfig.supabaseUrl.isNotEmpty ? 'SET' : 'EMPTY'}');
  print('Anon Key: ${SupabaseConfig.supabaseAnonKey.isNotEmpty ? 'SET' : 'EMPTY'}');
  print('Service Role Key: ${SupabaseConfig.supabaseServiceRoleKey.isNotEmpty ? 'SET' : 'EMPTY'}');
  print('Environment: ${SupabaseConfig.environment}');
  
  // Test validation
  final isValid = SupabaseConfig.validateConfig();
  print('Configuration Valid: $isValid');
  
  // Test connection info
  final connectionInfo = SupabaseConfig.getConnectionInfo();
  print('Connection Info: $connectionInfo');
  
  print('Test completed!');
}
