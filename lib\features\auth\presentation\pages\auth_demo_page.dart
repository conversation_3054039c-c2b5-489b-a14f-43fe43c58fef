import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/responsive_layout.dart';
import '../../../../app/config/app_router.dart';
import '../../../../core/auth/presentation/auth_widgets.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../../../core/auth/domain/auth_state.dart';

/// Demo page untuk menunjukkan implementasi autentikasi Supabase
class AuthDemoPage extends StatefulWidget {
  const AuthDemoPage({super.key});

  @override
  State<AuthDemoPage> createState() => _AuthDemoPageState();
}

class _AuthDemoPageState extends State<AuthDemoPage> {
  final Logger _logger = Logger();
  
  // Form controllers
  final _signUpEmailController = TextEditingController();
  final _signUpPasswordController = TextEditingController();
  final _signInEmailController = TextEditingController();
  final _signInPasswordController = TextEditingController();
  
  // Form keys
  final _signUpFormKey = GlobalKey<FormState>();
  final _signInFormKey = GlobalKey<FormState>();
  
  // Loading states
  bool _isSignUpLoading = false;
  bool _isSignInLoading = false;
  bool _isAnonymousLoading = false;

  @override
  void dispose() {
    _signUpEmailController.dispose();
    _signUpPasswordController.dispose();
    _signInEmailController.dispose();
    _signInPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Authentication Demo',
          style: AppTypography.h5.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.neutralWhite,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: AuthBuilder(
        builder: (context, authState) {
          _logger.d('Auth state in demo: ${authState.runtimeType}');
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: ResponsiveLayout(
              mobile: _buildMobileLayout(authState),
              tablet: _buildTabletLayout(authState), 
              desktop: _buildDesktopLayout(authState),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMobileLayout(AuthState authState) {
    return Column(
      children: [
        _buildAuthStateDisplay(authState),
        const SizedBox(height: AppSpacing.xl),
        _buildAuthContent(authState),
      ],
    );
  }

  Widget _buildTabletLayout(AuthState authState) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left panel - Auth state
        Expanded(
          flex: 1,
          child: _buildAuthStateDisplay(authState),
        ),
        const SizedBox(width: AppSpacing.xl),
        // Right panel - Auth forms
        Expanded(
          flex: 2,
          child: _buildAuthContent(authState),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(AuthState authState) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left panel - Auth state
        Expanded(
          flex: 1,
          child: _buildAuthStateDisplay(authState),
        ),
        const SizedBox(width: AppSpacing.xxl),
        // Right panel - Auth forms
        Expanded(
          flex: 2,
          child: _buildAuthContent(authState),
        ),
      ],
    );
  }

  Widget _buildAuthStateDisplay(AuthState authState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Current Auth State:', style: AppTypography.h5),
            const SizedBox(height: AppSpacing.md),
            _buildStateInfo(authState),
          ],
        ),
      ),
    );
  }

  Widget _buildStateInfo(AuthState authState) {
    switch (authState) {
      case AuthLoadingState():
        return const Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: AppSpacing.md),
            Text('Loading...'),
          ],
        );
        
      case AuthErrorState():
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error, color: AppColors.errorRed),
                const SizedBox(width: AppSpacing.sm),
                Text('Error', style: AppTypography.h6),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              authState.errorMessage ?? 'Unknown error occurred',
              style: AppTypography.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        );
        
      case UnauthenticatedState():
        return Row(
          children: [
            Icon(Icons.logout, color: AppColors.secondary),
            const SizedBox(width: AppSpacing.sm),
            Text('Not authenticated', style: AppTypography.bodyMedium),
          ],
        );
        
      case AuthenticatedState():
        return _buildAuthenticatedInfo(authState.user);
        
      case AnonymousState():
        return _buildAnonymousInfo(authState.user);
        
      default:
        return Text('Unknown state: ${authState.runtimeType}');
    }
  }

  Widget _buildAuthContent(AuthState authState) {
    switch (authState) {
      case AuthenticatedState():
        return _buildAuthenticatedContent(authState.user);
      case AnonymousState():
        return _buildAnonymousContent(authState.user);
      default:
        return _buildUnauthenticatedContent();
    }
  }

  Widget _buildUnauthenticatedContent() {
    return Column(
      children: [
        // Sign Up Form
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Form(
              key: _signUpFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Sign Up', style: AppTypography.h5),
                  const SizedBox(height: AppSpacing.md),
                  TextFormField(
                    controller: _signUpEmailController,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      hintText: 'Enter your email',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter email';
                      }
                      if (!value.contains('@')) {
                        return 'Please enter valid email';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: AppSpacing.md),
                  TextFormField(
                    controller: _signUpPasswordController,
                    decoration: const InputDecoration(
                      labelText: 'Password',
                      hintText: 'Enter password (min. 6 characters)',
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter password';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: AppSpacing.lg),
                  Text(
            'Create a new account with email and password',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.neutralGray600.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
                  const SizedBox(height: AppSpacing.md),
                  SizedBox(
                    width: double.infinity,
                    child: AppButtonFactory.primary(
                      text: 'Sign Up',
                      onPressed: _isSignUpLoading ? null : _handleSignUp,
                      isLoading: _isSignUpLoading,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        const SizedBox(height: AppSpacing.lg),
        
        // Sign In Form
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Form(
              key: _signInFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Sign In', style: AppTypography.h5),
                  const SizedBox(height: AppSpacing.md),
                  TextFormField(
                    controller: _signInEmailController,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      hintText: 'Enter your email',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter email';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: AppSpacing.md),
                  TextFormField(
                    controller: _signInPasswordController,
                    decoration: const InputDecoration(
                      labelText: 'Password',
                      hintText: 'Enter your password',
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter password';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: AppSpacing.lg),
                  Text(
          'Sign in with existing credentials',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.neutralGray600.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
                  const SizedBox(height: AppSpacing.md),
                  SizedBox(
                    width: double.infinity,
                    child: AppButtonFactory.primary(
                      text: 'Sign In',
                      onPressed: _isSignInLoading ? null : _handleSignIn,
                      isLoading: _isSignInLoading,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        const SizedBox(height: AppSpacing.lg),
        
        // Anonymous Sign In
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Column(
              children: [
                Text('Guest Access', style: AppTypography.h5),
                const SizedBox(height: AppSpacing.md),
                Text(
                  'Browse the app without creating an account',
                  style: AppTypography.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppSpacing.lg),
                SizedBox(
                  width: double.infinity,
                  child: AppButtonFactory.outline(
                    text: 'Continue as Guest',
                    onPressed: _isAnonymousLoading ? null : _handleAnonymousSignIn,
                    isLoading: _isAnonymousLoading,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAuthenticatedContent(user) {
    return Column(
      children: [
        Icon(
          Icons.check_circle,
          color: AppColors.successGreen,
          size: 64,
        ),
        const SizedBox(height: AppSpacing.lg),
        Text(
          'Welcome!',
          style: AppTypography.h4.copyWith(
            color: AppColors.successGreen,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.md),
        Text(
          'Role: ${user?.roleDisplayName ?? 'Unknown'}',
          style: AppTypography.h5,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Email: ${user?.email ?? 'No email'}',
          style: AppTypography.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.xl),
        
        // User info card
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('User Information:', style: AppTypography.h5),
                const SizedBox(height: AppSpacing.md),
                Text('ID: ${user?.id}', style: AppTypography.bodyMedium),
                Text('Role: ${user?.role}', style: AppTypography.bodyMedium),
                Text('SPPG: ${user?.sppgName ?? 'Not assigned'}', style: AppTypography.bodyMedium),
                Text('Anonymous: ${user?.isAnonymous ?? false}', style: AppTypography.bodyMedium),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: AppSpacing.xl),
        
        // Action buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            AppButtonFactory.outline(
              text: 'Go to Dashboard',
              onPressed: () => AppRouter.goToDashboard(context),
            ),
            AppButtonFactory.danger(
              text: 'Sign Out',
              onPressed: _handleSignOut,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAnonymousContent(user) {
    return Column(
      children: [
        Icon(
          Icons.visibility,
          color: AppColors.warningYellow,
          size: 64,
        ),
        const SizedBox(height: AppSpacing.lg),
        Text(
          'Guest Mode',
          style: AppTypography.h4.copyWith(
            color: AppColors.warningYellow,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.md),
        Text(
          'You are browsing as a guest',
          style: AppTypography.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.xl),
        
        // Guest info card
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Guest Information:', style: AppTypography.h5),
                const SizedBox(height: AppSpacing.md),
                Text('ID: ${user?.id}', style: AppTypography.bodyMedium),
                Text('Role: ${user?.role}', style: AppTypography.bodyMedium),
                Text('Limited access to features', style: AppTypography.bodyMedium),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: AppSpacing.xl),
        
        // Action buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            AppButtonFactory.outline(
              text: 'Go to Dashboard',
              onPressed: () => AppRouter.goToDashboard(context),
            ),
            AppButtonFactory.danger(
              text: 'Sign Out',
              onPressed: _handleSignOut,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAuthenticatedInfo(user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.successGreen),
            const SizedBox(width: AppSpacing.sm),
            Text('Authenticated', style: AppTypography.h6),
          ],
        ),
        const SizedBox(height: AppSpacing.sm),
        Text('Email: ${user?.email ?? 'No email'}'),
        Text('Role: ${user?.role ?? 'No role'}'),
        Text('SPPG: ${user?.sppgName ?? 'Not assigned'}'),
      ],
    );
  }

  Widget _buildAnonymousInfo(user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.visibility, color: AppColors.warningYellow),
            const SizedBox(width: AppSpacing.sm),
            Text('Anonymous', style: AppTypography.h6),
          ],
        ),
        const SizedBox(height: AppSpacing.sm),
        Text('ID: ${user?.id ?? 'No ID'}'),
        Text('Limited access'),
      ],
    );
  }

  // Auth methods
  Future<void> _handleSignUp() async {
    if (!_signUpFormKey.currentState!.validate()) return;
    
    setState(() => _isSignUpLoading = true);
    _logger.d('Starting sign up process');
    
    try {
      await AuthService.instance.signUpWithEmail(
        email: _signUpEmailController.text.trim(),
        password: _signUpPasswordController.text,
        nama: 'Demo User', // Default nama untuk demo
        role: 'kepala_dapur', // Default role untuk demo
      );
      _logger.i('Sign up completed successfully');
      
      // Clear form
      _signUpEmailController.clear();
      _signUpPasswordController.clear();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sign up successful! Please check your email for verification.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _logger.e('Sign up error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sign up failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSignUpLoading = false);
      }
    }
  }

  Future<void> _handleSignIn() async {
    if (!_signInFormKey.currentState!.validate()) return;
    
    setState(() => _isSignInLoading = true);
    _logger.d('Starting sign in process');
    
    try {
      await AuthService.instance.signInWithEmail(
        email: _signInEmailController.text.trim(),
        password: _signInPasswordController.text,
      );
      _logger.i('Sign in completed successfully');
      
      // Clear form
      _signInEmailController.clear();
      _signInPasswordController.clear();
      
    } catch (e) {
      _logger.e('Sign in error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sign in failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSignInLoading = false);
      }
    }
  }

  Future<void> _handleAnonymousSignIn() async {
    setState(() => _isAnonymousLoading = true);
    _logger.d('Starting anonymous sign in');
    
    try {
      await AuthService.instance.signInAnonymously();
      _logger.i('Anonymous sign in completed successfully');
    } catch (e) {
      _logger.e('Anonymous sign in error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Guest access failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isAnonymousLoading = false);
      }
    }
  }

  Future<void> _handleSignOut() async {
    _logger.d('Starting sign out process');
    
    try {
      await AuthService.instance.signOut();
      _logger.i('Sign out completed successfully');
    } catch (e) {
      _logger.e('Sign out error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sign out failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
