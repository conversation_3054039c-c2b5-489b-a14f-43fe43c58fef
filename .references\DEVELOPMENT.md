# SOD-MBG Development Guide

## Environment Variables
Copy `.env.example` to `.env` and configure with your actual values:

```bash
cp .env.example .env
```

## Getting Started

### Prerequisites
- Flutter SDK (3.7.2 or higher)
- Dart SDK
- Supabase account
- PowerSync account (for offline-first functionality)

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```
3. Configure environment variables in `.env`
4. Run the application:
   ```bash
   flutter run
   ```

## Project Structure
- `/lib/app/` - Application configuration and core components
- `/lib/data/` - Data models, providers, and repositories
- `/lib/features/` - Feature-based modules (auth, dashboard, kitchen management, etc.)

## Key Features
- Cross-platform support (Android, iOS, Web, Windows, macOS, Linux)
- Offline-first architecture with PowerSync
- Role-based access control (6 user roles)
- Kitchen operational management
- Real-time inventory tracking
- Financial management and reporting

## Contributing
Please refer to the coding guidelines in `.github/copilot-instructions.md` for development standards and practices.
