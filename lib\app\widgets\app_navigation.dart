import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';

/// Komponen navigasi untuk aplikasi SOD-MBG
/// Menyediakan berbagai jenis navigasi yang konsisten

/// Bottom Navigation Bar dengan styling yang konsisten
class AppBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<AppNavigationItem> items;
  final bool showLabels;
  final BottomNavigationBarType type;

  const AppBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.showLabels = true,
    this.type = BottomNavigationBarType.fixed,
  });

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: onTap,
      type: type,
      backgroundColor: AppColors.backgroundPrimary,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.textTertiary,
      selectedLabelStyle: AppTypography.labelSmall.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTypography.labelSmall,
      showSelectedLabels: showLabels,
      showUnselectedLabels: showLabels,
      elevation: AppElevation.card,
      items: items
          .map((item) => BottomNavigationBarItem(
                icon: Icon(item.icon),
                activeIcon: Icon(item.activeIcon ?? item.icon),
                label: item.label,
                tooltip: item.tooltip,
              ))
          .toList(),
    );
  }
}

/// Navigation Rail untuk layout yang lebih luas
class AppNavigationRail extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onDestinationSelected;
  final List<AppNavigationItem> destinations;
  final bool extended;
  final Widget? leading;
  final Widget? trailing;

  const AppNavigationRail({
    super.key,
    required this.selectedIndex,
    required this.onDestinationSelected,
    required this.destinations,
    this.extended = false,
    this.leading,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return NavigationRail(
      selectedIndex: selectedIndex,
      onDestinationSelected: onDestinationSelected,
      extended: extended,
      leading: leading,
      trailing: trailing,
      backgroundColor: AppColors.backgroundPrimary,
      selectedIconTheme: IconThemeData(
        color: AppColors.primary,
        size: AppIcons.sizeMedium,
      ),
      unselectedIconTheme: IconThemeData(
        color: AppColors.textTertiary,
        size: AppIcons.sizeMedium,
      ),
      selectedLabelTextStyle: AppTypography.labelMedium.copyWith(
        color: AppColors.primary,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelTextStyle: AppTypography.labelMedium.copyWith(
        color: AppColors.textTertiary,
      ),
      destinations: destinations
          .map((item) => NavigationRailDestination(
                icon: Icon(item.icon),
                selectedIcon: Icon(item.activeIcon ?? item.icon),
                label: Text(item.label),
              ))
          .toList(),
    );
  }
}

/// Drawer untuk navigasi sidebar
class AppDrawer extends StatelessWidget {
  final String? headerTitle;
  final String? headerSubtitle;
  final Widget? headerIcon;
  final List<AppDrawerItem> items;
  final VoidCallback? onHeaderTap;

  const AppDrawer({
    super.key,
    this.headerTitle,
    this.headerSubtitle,
    this.headerIcon,
    required this.items,
    this.onHeaderTap,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.backgroundPrimary,
      child: Column(
        children: [
          if (headerTitle != null)
            DrawerHeader(
              decoration: BoxDecoration(
                color: AppColors.primary,
              ),
              child: InkWell(
                onTap: onHeaderTap,
                child: Row(
                  children: [
                    if (headerIcon != null) ...[
                      headerIcon!,
                      const SizedBox(width: AppSpacing.md),
                    ],
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            headerTitle!,
                            style: AppTypography.h6.copyWith(
                              color: AppColors.textOnPrimary,
                            ),
                          ),
                          if (headerSubtitle != null) ...[
                            const SizedBox(height: AppSpacing.xs),
                            Text(
                              headerSubtitle!,
                              style: AppTypography.bodySmall.copyWith(
                                color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: items.map((item) => _buildDrawerItem(item)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(AppDrawerItem item) {
    if (item.isDivider) {
      return const Divider(
        color: AppColors.neutralGray300,
        height: 1,
      );
    }

    if (item.isHeader) {
      return Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Text(
          item.title,
          style: AppTypography.labelMedium.copyWith(
            color: AppColors.textTertiary,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    return ListTile(
      leading: item.icon != null
          ? Icon(
              item.icon,
              color: item.isSelected 
                  ? AppColors.primary 
                  : AppColors.textTertiary,
            )
          : null,
      title: Text(
        item.title,
        style: AppTypography.bodyMedium.copyWith(
          color: item.isSelected 
              ? AppColors.primary 
              : AppColors.textPrimary,
          fontWeight: item.isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      subtitle: item.subtitle != null
          ? Text(
              item.subtitle!,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textTertiary,
              ),
            )
          : null,
      trailing: item.trailing,
      selected: item.isSelected,
      selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
      onTap: item.onTap,
    );
  }
}

/// Breadcrumb navigation
class AppBreadcrumb extends StatelessWidget {
  final List<AppBreadcrumbItem> items;
  final String separator;

  const AppBreadcrumb({
    super.key,
    required this.items,
    this.separator = ' > ',
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isLast = index == items.length - 1;

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.onTap != null)
              InkWell(
                onTap: item.onTap,
                child: Text(
                  item.title,
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              )
            else
              Text(
                item.title,
                style: AppTypography.bodyMedium.copyWith(
                  color: isLast 
                      ? AppColors.textPrimary 
                      : AppColors.textTertiary,
                  fontWeight: isLast ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            if (!isLast) ...[
              const SizedBox(width: AppSpacing.xs),
              Text(
                separator,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textTertiary,
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
            ],
          ],
        );
      }).toList(),
    );
  }
}

/// Tab Bar dengan styling yang konsisten
class AppTabBar extends StatelessWidget {
  final TabController controller;
  final List<AppTabItem> tabs;
  final bool isScrollable;
  final TabBarIndicatorSize indicatorSize;

  const AppTabBar({
    super.key,
    required this.controller,
    required this.tabs,
    this.isScrollable = false,
    this.indicatorSize = TabBarIndicatorSize.label,
  });

  @override
  Widget build(BuildContext context) {
    return TabBar(
      controller: controller,
      tabs: tabs
          .map((tab) => Tab(
                text: tab.title,
                icon: tab.icon != null ? Icon(tab.icon) : null,
              ))
          .toList(),
      isScrollable: isScrollable,
      indicatorColor: AppColors.primary,
      indicatorSize: indicatorSize,
      labelColor: AppColors.primary,
      unselectedLabelColor: AppColors.textTertiary,
      labelStyle: AppTypography.labelMedium.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTypography.labelMedium,
    );
  }
}

/// App Bar dengan styling yang konsisten
class AppTopBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final Color? backgroundColor;
  final double? elevation;
  final PreferredSizeWidget? bottom;

  const AppTopBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.backgroundColor,
    this.elevation,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: titleWidget ??
          (title != null
              ? Text(
                  title!,
                  style: AppTypography.h6.copyWith(
                    color: AppColors.textOnPrimary,
                  ),
                )
              : null),
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppColors.primary,
      elevation: elevation ?? AppElevation.appBar,
      bottom: bottom,
      iconTheme: IconThemeData(
        color: AppColors.textOnPrimary,
        size: AppIcons.sizeMedium,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0),
      );
}

/// Floating Action Button dengan styling yang konsisten
class AppFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final IconData? icon;
  final String? heroTag;
  final bool mini;

  const AppFloatingActionButton({
    super.key,
    this.onPressed,
    this.child,
    this.tooltip,
    this.icon,
    this.heroTag,
    this.mini = false,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      heroTag: heroTag,
      mini: mini,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      elevation: AppElevation.fab,
      child: child ?? (icon != null ? Icon(icon) : null),
    );
  }
}

/// Stepper Navigation untuk workflow
class AppStepperNavigation extends StatelessWidget {
  final int currentStep;
  final List<AppStepItem> steps;
  final ValueChanged<int>? onStepTapped;
  final VoidCallback? onStepContinue;
  final VoidCallback? onStepCancel;
  final bool canGoBack;
  final bool canGoNext;

  const AppStepperNavigation({
    super.key,
    required this.currentStep,
    required this.steps,
    this.onStepTapped,
    this.onStepContinue,
    this.onStepCancel,
    this.canGoBack = true,
    this.canGoNext = true,
  });

  @override
  Widget build(BuildContext context) {
    return Stepper(
      currentStep: currentStep,
      onStepTapped: onStepTapped,
      onStepContinue: canGoNext ? onStepContinue : null,
      onStepCancel: canGoBack ? onStepCancel : null,
      controlsBuilder: (context, details) {
        return Row(
          children: [
            if (details.stepIndex < steps.length - 1 && canGoNext)
              ElevatedButton(
                onPressed: details.onStepContinue,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                ),
                child: const Text('Lanjut'),
              ),
            if (details.stepIndex == steps.length - 1 && canGoNext)
              ElevatedButton(
                onPressed: details.onStepContinue,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                ),
                child: const Text('Selesai'),
              ),
            const SizedBox(width: AppSpacing.sm),
            if (details.stepIndex > 0 && canGoBack)
              OutlinedButton(
                onPressed: details.onStepCancel,
                child: const Text('Kembali'),
              ),
          ],
        );
      },
      steps: steps
          .asMap()
          .entries
          .map((entry) => Step(
                title: Text(
                  entry.value.title,
                  style: AppTypography.labelMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                content: entry.value.content,
                isActive: entry.key == currentStep,
                state: _getStepState(entry.key),
              ))
          .toList(),
    );
  }

  StepState _getStepState(int index) {
    if (index < currentStep) {
      return StepState.complete;
    } else if (index == currentStep) {
      return StepState.editing;
    } else {
      return StepState.disabled;
    }
  }
}

/// Model untuk navigation item
class AppNavigationItem {
  final IconData icon;
  final IconData? activeIcon;
  final String label;
  final String? tooltip;

  const AppNavigationItem({
    required this.icon,
    this.activeIcon,
    required this.label,
    this.tooltip,
  });
}

/// Model untuk drawer item
class AppDrawerItem {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool isDivider;
  final bool isHeader;

  const AppDrawerItem({
    required this.title,
    this.subtitle,
    this.icon,
    this.trailing,
    this.onTap,
    this.isSelected = false,
    this.isDivider = false,
    this.isHeader = false,
  });

  /// Factory untuk membuat divider
  const AppDrawerItem.divider()
      : title = '',
        subtitle = null,
        icon = null,
        trailing = null,
        onTap = null,
        isSelected = false,
        isDivider = true,
        isHeader = false;

  /// Factory untuk membuat header
  const AppDrawerItem.header(String title)
      : title = title,
        subtitle = null,
        icon = null,
        trailing = null,
        onTap = null,
        isSelected = false,
        isDivider = false,
        isHeader = true;
}

/// Model untuk breadcrumb item
class AppBreadcrumbItem {
  final String title;
  final VoidCallback? onTap;

  const AppBreadcrumbItem({
    required this.title,
    this.onTap,
  });
}

/// Model untuk tab item
class AppTabItem {
  final String title;
  final IconData? icon;

  const AppTabItem({
    required this.title,
    this.icon,
  });
}

/// Model untuk step item
class AppStepItem {
  final String title;
  final Widget content;

  const AppStepItem({
    required this.title,
    required this.content,
  });
}
