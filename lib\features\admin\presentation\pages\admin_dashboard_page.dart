import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/app_button.dart';

/// Dashboard sederhana untuk role Admin Yayasan
/// Menampilkan Total Porsi Terdistribusi Hari Ini dan ringkasan operasional
class AdminDashboardPage extends StatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  State<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends State<AdminDashboardPage> {
  final Logger _logger = Logger();
  
  // Mock data untuk dashboard
  final int _totalDistributedPortions = 2450;
  final int _totalSppg = 5;
  final int _activeSppg = 5;
  final int _totalDeliveries = 12;
  final int _completedDeliveries = 8;
  final double _budgetUtilization = 0.75; // 75%
  final int _totalBeneficiaries = 2450;

  @override
  void initState() {
    super.initState();
    _logger.i('AdminDashboardPage initialized');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Admin Yayasan'),
        backgroundColor: AppColors.adminYayasanColor,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(AppIcons.refresh),
            onPressed: _refreshDashboard,
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildHeader(),
              _buildMainMetrics(),
              _buildOperationalSummary(),
              _buildQuickActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.adminYayasanColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppRadius.lg),
          bottomRight: Radius.circular(AppRadius.lg),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selamat Datang, Admin Yayasan',
            style: AppTypography.h5.copyWith(
              color: AppColors.textOnPrimary,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Pantau dan kelola operasional SPPG secara real-time',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            children: [
              Icon(
                AppIcons.calendar,
                size: AppIcons.sizeSmall,
                color: AppColors.textOnPrimary,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                _formatDate(DateTime.now()),
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainMetrics() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Distribusi Hari Ini',
            style: AppTypography.h6,
          ),
          const SizedBox(height: AppSpacing.md),
          
          // Main metric card - Total Porsi Terdistribusi
          AppCardFactory.metric(
            title: 'Total Porsi Terdistribusi',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      AppIcons.restaurant,
                      size: AppIcons.sizeLarge,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _formatNumber(_totalDistributedPortions),
                            style: AppTypography.h2.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          Text(
                            'porsi makanan',
                            style: AppTypography.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          
          // Secondary metrics
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Penerima Manfaat',
                  _formatNumber(_totalBeneficiaries),
                  'siswa',
                  AppColors.infoBlue,
                  AppIcons.userGroup,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildMetricCard(
                  'Pengiriman Selesai',
                  '$_completedDeliveries/$_totalDeliveries',
                  'pengiriman',
                  AppColors.successGreen,
                  AppIcons.checkmark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOperationalSummary() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ringkasan Operasional',
            style: AppTypography.h6,
          ),
          const SizedBox(height: AppSpacing.md),
          
          AppCardFactory.basic(
            child: Column(
              children: [
                _buildSummaryRow(
                  'SPPG Aktif',
                  '$_activeSppg/$_totalSppg',
                  AppIcons.kitchen,
                  AppColors.primary,
                ),
                const Divider(),
                _buildSummaryRow(
                  'Pengiriman Berlangsung',
                  '${_totalDeliveries - _completedDeliveries}',
                  AppIcons.delivery,
                  AppColors.warningYellow,
                ),
                const Divider(),
                _buildSummaryRow(
                  'Utilisasi Anggaran',
                  '${(_budgetUtilization * 100).toInt()}%',
                  AppIcons.budget,
                  AppColors.infoBlue,
                ),
                const Divider(),
                _buildSummaryRow(
                  'Status Sistem',
                  'Semua Normal',
                  AppIcons.statusSuccess,
                  AppColors.successGreen,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Aksi Cepat',
            style: AppTypography.h6,
          ),
          const SizedBox(height: AppSpacing.md),
          
          Row(
            children: [
              Expanded(
                child: AppButtonFactory.outline(
                  text: 'Lihat Laporan',
                  icon: AppIcons.report,
                  onPressed: () => _showReports(),
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: AppButtonFactory.secondary(
                  text: 'Kelola SPPG',
                  icon: AppIcons.kitchen,
                  onPressed: () => _manageSppg(),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          
          Row(
            children: [
              Expanded(
                child: AppButtonFactory.outline(
                  text: 'Audit Digital',
                  icon: AppIcons.inspection,
                  onPressed: () => _digitalAudit(),
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: AppButtonFactory.primary(
                  text: 'Pengaturan',
                  icon: AppIcons.settings,
                  onPressed: () => _showSettings(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    String subtitle,
    Color color,
    IconData icon,
  ) {
    return AppCardFactory.basic(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: AppIcons.sizeMedium,
                color: color,
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Text(
                  title,
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            value,
            style: AppTypography.h4.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            subtitle,
            style: AppTypography.labelSmall.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      child: Row(
        children: [
          Icon(
            icon,
            size: AppIcons.sizeMedium,
            color: color,
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              label,
              style: AppTypography.bodyMedium,
            ),
          ),
          Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _refreshDashboard() {
    _logger.i('Refreshing dashboard data');
    
    // Simulasi refresh
    setState(() {
      // Data akan di-refresh dari server
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Dashboard berhasil diperbarui'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showReports() {
    _logger.i('Showing reports');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Laporan'),
        content: const Text('Fitur laporan akan segera tersedia.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _manageSppg() {
    _logger.i('Managing SPPG');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Kelola SPPG'),
        content: const Text('Fitur pengelolaan SPPG akan segera tersedia.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _digitalAudit() {
    _logger.i('Starting digital audit');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audit Digital'),
        content: const Text('Fitur audit digital akan segera tersedia.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    _logger.i('Showing settings');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pengaturan'),
        content: const Text('Fitur pengaturan akan segera tersedia.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
      'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }
}
