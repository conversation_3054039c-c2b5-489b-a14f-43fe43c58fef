import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';
import 'app_button.dart';

/// Komponen untuk notifikasi dan feedback dalam aplikasi
/// Menyediakan berbagai jenis notifikasi yang konsisten dengan design system
class AppNotifications {
  AppNotifications._();

  /// Menampilkan snackbar dengan styling yang konsisten
  static void showSnackBar(
    BuildContext context, {
    required String message,
    NotificationType type = NotificationType.info,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    final colorScheme = _getColorScheme(type);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getIcon(type),
              color: colorScheme.onPrimary,
              size: AppIcons.sizeSmall,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                message,
                style: AppTypography.bodyMedium.copyWith(
                  color: colorScheme.onPrimary,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: colorScheme.primary,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.md),
        ),
        margin: const EdgeInsets.all(AppSpacing.md),
        action: onAction != null && actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: colorScheme.onPrimary,
                onPressed: onAction,
              )
            : null,
      ),
    );
  }

  /// Menampilkan banner notification
  static void showBanner(
    BuildContext context, {
    required String message,
    NotificationType type = NotificationType.info,
    Duration? duration,
    VoidCallback? onDismiss,
    List<Widget>? actions,
  }) {
    final colorScheme = _getColorScheme(type);
    
    ScaffoldMessenger.of(context).showMaterialBanner(
      MaterialBanner(
        content: Row(
          children: [
            Icon(
              _getIcon(type),
              color: colorScheme.primary,
              size: AppIcons.sizeMedium,
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Text(
                message,
                style: AppTypography.bodyMedium,
              ),
            ),
          ],
        ),
        backgroundColor: colorScheme.surface,
        actions: actions ?? [
          TextButton(
            onPressed: onDismiss ?? () {
              ScaffoldMessenger.of(context).hideCurrentMaterialBanner();
            },
            child: const Text('Tutup'),
          ),
        ],
      ),
    );

    // Auto dismiss jika ada duration
    if (duration != null) {
      Future.delayed(duration, () {
        if (context.mounted) {
          ScaffoldMessenger.of(context).hideCurrentMaterialBanner();
        }
      });
    }
  }

  /// Menampilkan dialog notification
  static Future<void> showNotificationDialog(
    BuildContext context, {
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    List<Widget>? actions,
  }) async {
    final colorScheme = _getColorScheme(type);
    
    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          _getIcon(type),
          color: colorScheme.primary,
          size: AppIcons.sizeLarge,
        ),
        title: Text(
          title,
          style: AppTypography.h6,
          textAlign: TextAlign.center,
        ),
        content: Text(
          message,
          style: AppTypography.bodyMedium,
          textAlign: TextAlign.center,
        ),
        actions: actions ?? [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Menampilkan bottom sheet notification
  static Future<void> showBottomSheet(
    BuildContext context, {
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    List<Widget>? actions,
  }) async {
    final colorScheme = _getColorScheme(type);
    
    await showModalBottomSheet<void>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppRadius.bottomSheet),
        ),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.neutralGray300,
                borderRadius: BorderRadius.circular(AppRadius.sm),
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            
            // Icon
            Icon(
              _getIcon(type),
              color: colorScheme.primary,
              size: AppIcons.sizeXLarge,
            ),
            const SizedBox(height: AppSpacing.md),
            
            // Title
            Text(
              title,
              style: AppTypography.h5,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            
            // Message
            Text(
              message,
              style: AppTypography.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            
            // Actions
            if (actions != null) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: actions,
              ),
            ] else ...[
              AppButtonFactory.primary(
                text: 'OK',
                onPressed: () => Navigator.of(context).pop(),
                isFullWidth: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  static _NotificationColorScheme _getColorScheme(NotificationType type) {
    switch (type) {
      case NotificationType.success:
        return _NotificationColorScheme(
          primary: AppColors.successGreen,
          onPrimary: AppColors.textOnPrimary,
          surface: AppColors.successGreen.withValues(alpha: 0.1),
        );
      case NotificationType.warning:
        return _NotificationColorScheme(
          primary: AppColors.warningYellow,
          onPrimary: AppColors.textPrimary,
          surface: AppColors.warningYellow.withValues(alpha: 0.1),
        );
      case NotificationType.error:
        return _NotificationColorScheme(
          primary: AppColors.errorRed,
          onPrimary: AppColors.textOnPrimary,
          surface: AppColors.errorRed.withValues(alpha: 0.1),
        );
      case NotificationType.info:
        return _NotificationColorScheme(
          primary: AppColors.infoBlue,
          onPrimary: AppColors.textOnPrimary,
          surface: AppColors.infoBlue.withValues(alpha: 0.1),
        );
    }
  }

  static IconData _getIcon(NotificationType type) {
    switch (type) {
      case NotificationType.success:
        return AppIcons.checkmark;
      case NotificationType.warning:
        return AppIcons.warning;
      case NotificationType.error:
        return AppIcons.error;
      case NotificationType.info:
        return AppIcons.info;
    }
  }
}

/// Widget untuk menampilkan notifikasi inline
class AppInlineNotification extends StatelessWidget {
  final String message;
  final NotificationType type;
  final VoidCallback? onDismiss;
  final List<Widget>? actions;

  const AppInlineNotification({
    super.key,
    required this.message,
    required this.type,
    this.onDismiss,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = AppNotifications._getColorScheme(type);
    
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(
          color: colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            AppNotifications._getIcon(type),
            color: colorScheme.primary,
            size: AppIcons.sizeMedium,
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message,
                  style: AppTypography.bodyMedium,
                ),
                if (actions != null) ...[
                  const SizedBox(height: AppSpacing.sm),
                  Row(
                    children: actions!,
                  ),
                ],
              ],
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: AppSpacing.sm),
            IconButton(
              onPressed: onDismiss,
              icon: const Icon(AppIcons.close),
              iconSize: AppIcons.sizeSmall,
              color: colorScheme.primary,
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget untuk menampilkan toast notification
class AppToast extends StatefulWidget {
  final String message;
  final NotificationType type;
  final Duration duration;
  final VoidCallback? onDismiss;

  const AppToast({
    super.key,
    required this.message,
    required this.type,
    this.duration = const Duration(seconds: 3),
    this.onDismiss,
  });

  @override
  State<AppToast> createState() => _AppToastState();

  static void show(
    BuildContext context, {
    required String message,
    NotificationType type = NotificationType.info,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onDismiss,
  }) {
    final overlay = Overlay.of(context);
    final entry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + AppSpacing.lg,
        left: AppSpacing.md,
        right: AppSpacing.md,
        child: Material(
          color: Colors.transparent,
          child: AppToast(
            message: message,
            type: type,
            duration: duration,
            onDismiss: onDismiss,
          ),
        ),
      ),
    );

    overlay.insert(entry);

    Future.delayed(duration, () {
      entry.remove();
      onDismiss?.call();
    });
  }
}

class _AppToastState extends State<AppToast>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_controller);

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = AppNotifications._getColorScheme(widget.type);
    
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: colorScheme.primary,
            borderRadius: BorderRadius.circular(AppRadius.md),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Icon(
                AppNotifications._getIcon(widget.type),
                color: colorScheme.onPrimary,
                size: AppIcons.sizeMedium,
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Text(
                  widget.message,
                  style: AppTypography.bodyMedium.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Widget untuk menampilkan alert card
class AppAlertCard extends StatelessWidget {
  final String title;
  final String message;
  final NotificationType type;
  final VoidCallback? onDismiss;
  final List<Widget>? actions;

  const AppAlertCard({
    super.key,
    required this.title,
    required this.message,
    required this.type,
    this.onDismiss,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = AppNotifications._getColorScheme(type);
    
    return Card(
      elevation: AppElevation.alert,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.lg),
        side: BorderSide(
          color: colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  AppNotifications._getIcon(type),
                  color: colorScheme.primary,
                  size: AppIcons.sizeMedium,
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Text(
                    title,
                    style: AppTypography.h6.copyWith(
                      color: colorScheme.primary,
                    ),
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    onPressed: onDismiss,
                    icon: const Icon(AppIcons.close),
                    iconSize: AppIcons.sizeSmall,
                    color: colorScheme.primary,
                  ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              message,
              style: AppTypography.bodyMedium,
            ),
            if (actions != null) ...[
              const SizedBox(height: AppSpacing.md),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: actions!,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Enum untuk jenis notifikasi
enum NotificationType {
  info,
  success,
  warning,
  error,
}

/// Helper class untuk color scheme notifikasi
class _NotificationColorScheme {
  final Color primary;
  final Color onPrimary;
  final Color surface;

  const _NotificationColorScheme({
    required this.primary,
    required this.onPrimary,
    required this.surface,
  });
}
