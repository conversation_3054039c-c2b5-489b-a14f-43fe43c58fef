import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';
import 'supabase_config.dart';

/// Service untuk menginisialisasi dan mengelola koneksi Supabase
/// Menggunakan singleton pattern untuk memastikan hanya ada satu instance
class SupabaseService {
  static final Logger _logger = Logger();
  
  // Private constructor untuk singleton pattern
  SupabaseService._();
  
  // Singleton instance
  static final SupabaseService _instance = SupabaseService._();
  static SupabaseService get instance => _instance;
  
  // Status inisialisasi
  bool _isInitialized = false;
  
  /// Getter untuk status inisialisasi
  bool get isInitialized => _isInitialized;
  
  /// Getter untuk Supabase client
  SupabaseClient get client => Supabase.instance.client;
  
  /// Getter untuk Auth client
  GoTrueClient get auth => client.auth;
  
  /// Getter untuk Functions client
  FunctionsClient get functions => client.functions;
  
  /// Getter untuk Database client
  SupabaseQueryBuilder get database => client.from('');
  
  /// Getter untuk current user
  User? get currentUser => auth.currentUser;
  
  /// Getter untuk current session
  Session? get currentSession => auth.currentSession;
  
  /// Check apakah user sudah login
  bool get isLoggedIn => currentUser != null;
  
  /// Check apakah user adalah anonymous
  bool get isAnonymous => currentUser?.isAnonymous ?? false;
  
  /// Inisialisasi Supabase
  Future<bool> initialize() async {
    if (_isInitialized) {
      _logger.d('Supabase already initialized');
      return true;
    }
    
    try {
      _logger.i('Initializing Supabase...');
      
      // Validasi konfigurasi
      if (!SupabaseConfig.validateConfig()) {
        _logger.e('Supabase configuration is invalid - using placeholder values');
        _logger.w('Please set proper SUPABASE_URL and SUPABASE_ANON_KEY environment variables');
        
        // For development, we'll throw an error instead of continuing
        throw Exception('Supabase configuration is invalid. Please check your environment variables.');
      }
      
      // Log konfigurasi untuk debugging
      SupabaseConfig.logConfig();
      
      // Inisialisasi Supabase
      await Supabase.initialize(
        url: SupabaseConfig.supabaseUrl,
        anonKey: SupabaseConfig.supabaseAnonKey,
        debug: SupabaseConfig.isDebug,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
          autoRefreshToken: true,
        ),
      );
      
      _isInitialized = true;
      _logger.i('Supabase initialized successfully');
      
      // Setup auth state listener
      _setupAuthStateListener();
      
      // Log connection info
      _logConnectionInfo();
      
      return true;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize Supabase: $e', stackTrace: stackTrace);
      return false;
    }
  }
  
  /// Setup listener untuk perubahan auth state
  void _setupAuthStateListener() {
    _logger.d('Setting up auth state listener');
    
    auth.onAuthStateChange.listen((data) {
      final event = data.event;
      final session = data.session;
      
      _logger.d('Auth state changed: $event');
      
      switch (event) {
        case AuthChangeEvent.signedIn:
          _logger.i('User signed in: ${session?.user.id}');
          _onUserSignedIn(session);
          break;
        case AuthChangeEvent.signedOut:
          _logger.i('User signed out');
          _onUserSignedOut();
          break;
        case AuthChangeEvent.tokenRefreshed:
          _logger.d('Token refreshed');
          break;
        case AuthChangeEvent.userUpdated:
          _logger.d('User updated');
          break;
        case AuthChangeEvent.passwordRecovery:
          _logger.d('Password recovery initiated');
          break;
        default:
          _logger.d('Unhandled auth event: $event');
      }
    });
  }
  
  /// Handler ketika user sign in
  void _onUserSignedIn(Session? session) {
    if (session?.user == null) return;
    
    final user = session!.user;
    _logger.i('User signed in successfully');
    _logger.d('User ID: ${user.id}');
    _logger.d('User Email: ${user.email ?? 'N/A'}');
    _logger.d('Is Anonymous: ${user.isAnonymous}');
    
    // TODO: Implement additional sign in logic
    // - Update user last login
    // - Sync user data
    // - Initialize user preferences
  }
  
  /// Handler ketika user sign out
  void _onUserSignedOut() {
    _logger.i('User signed out successfully');
    
    // TODO: Implement sign out cleanup
    // - Clear cached data
    // - Reset user preferences
    // - Cancel background tasks
  }
  
  /// Log informasi koneksi
  void _logConnectionInfo() {
    if (!SupabaseConfig.isDebug) return;
    
    _logger.d('Supabase Connection Info:');
    _logger.d('- Client initialized: ${SupabaseConfig.supabaseUrl}');
    _logger.d('- Auth client ready: ${auth.currentUser != null}');
    _logger.d('- Current user: ${currentUser?.id ?? 'None'}');
    _logger.d('- Is logged in: $isLoggedIn');
    _logger.d('- Is anonymous: $isAnonymous');
  }
  
  /// Reset service (untuk testing)
  @visibleForTesting
  void reset() {
    _isInitialized = false;
    _logger.d('Supabase service reset');
  }
  
  /// Dispose service
  void dispose() {
    _logger.d('Disposing Supabase service');
    // Cleanup jika diperlukan
  }
}
