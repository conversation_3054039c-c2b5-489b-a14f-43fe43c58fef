import 'package:equatable/equatable.dart';
import 'app_user.dart';

/// Base class untuk semua auth states
abstract class AuthState extends Equatable {
  const AuthState();
  
  @override
  List<Object?> get props => [];
}

/// State ketika auth sedang loading/initializing
class AuthInitialState extends AuthState {
  const AuthInitialState();
  
  @override
  String toString() => 'AuthInitialState';
}

/// State ketika auth sedang loading
class AuthLoadingState extends AuthState {
  const AuthLoadingState({this.message});
  
  final String? message;
  
  @override
  List<Object?> get props => [message];
  
  @override
  String toString() => 'AuthLoadingState(message: $message)';
}

/// State ketika user sudah authenticated
class AuthenticatedState extends AuthState {
  const AuthenticatedState({
    required this.user,
    this.isFirstLogin = false,
  });
  
  final AppUser user;
  final bool isFirstLogin;
  
  @override
  List<Object?> get props => [user, isFirstLogin];
  
  @override
  String toString() => 'AuthenticatedState(user: ${user.displayName}, isFirstLogin: $isFirstLogin)';
}

/// State ketika user belum authenticated
class UnauthenticatedState extends AuthState {
  const UnauthenticatedState({this.message});
  
  final String? message;
  
  @override
  List<Object?> get props => [message];
  
  @override
  String toString() => 'UnauthenticatedState(message: $message)';
}

/// State ketika terjadi error dalam authentication
class AuthErrorState extends AuthState {
  const AuthErrorState({
    required this.error,
    this.stackTrace,
    this.canRetry = true,
  });
  
  final String error;
  final StackTrace? stackTrace;
  final bool canRetry;
  
  @override
  List<Object?> get props => [error, stackTrace, canRetry];
  
  @override
  String toString() => 'AuthErrorState(error: $error, canRetry: $canRetry)';
}

/// State ketika session expired
class SessionExpiredState extends AuthState {
  const SessionExpiredState({this.message});
  
  final String? message;
  
  @override
  List<Object?> get props => [message];
  
  @override
  String toString() => 'SessionExpiredState(message: $message)';
}

/// State ketika user login sebagai anonymous
class AnonymousState extends AuthState {
  const AnonymousState({
    required this.user,
    this.expiresAt,
  });
  
  final AppUser user;
  final DateTime? expiresAt;
  
  @override
  List<Object?> get props => [user, expiresAt];
  
  @override
  String toString() => 'AnonymousState(user: ${user.displayName}, expiresAt: $expiresAt)';
}

/// State ketika sedang proses logout
class LoggingOutState extends AuthState {
  const LoggingOutState();
  
  @override
  String toString() => 'LoggingOutState';
}

/// Extensions untuk helper methods
extension AuthStateExtensions on AuthState {
  /// Check apakah state adalah authenticated
  bool get isAuthenticated => this is AuthenticatedState;
  
  /// Check apakah state adalah unauthenticated
  bool get isUnauthenticated => this is UnauthenticatedState;
  
  /// Check apakah state adalah error
  bool get isError => this is AuthErrorState;
  
  /// Check apakah state adalah loading
  bool get isLoading => this is AuthLoadingState;
  
  /// Check apakah state adalah anonymous
  bool get isAnonymous => this is AnonymousState;
  
  /// Check apakah state adalah session expired
  bool get isSessionExpired => this is SessionExpiredState;
  
  /// Get user jika ada
  AppUser? get user {
    if (this is AuthenticatedState) {
      return (this as AuthenticatedState).user;
    }
    if (this is AnonymousState) {
      return (this as AnonymousState).user;
    }
    return null;
  }
  
  /// Get error message jika ada
  String? get errorMessage {
    if (this is AuthErrorState) {
      return (this as AuthErrorState).error;
    }
    if (this is UnauthenticatedState) {
      return (this as UnauthenticatedState).message;
    }
    if (this is SessionExpiredState) {
      return (this as SessionExpiredState).message;
    }
    return null;
  }
}
