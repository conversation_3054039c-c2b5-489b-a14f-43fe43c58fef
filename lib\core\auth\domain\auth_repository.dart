import 'app_user.dart';
import 'auth_state.dart';

/// Repository interface untuk authentication
/// Mendefinisikan contract untuk operasi auth
abstract class AuthRepository {
  
  // ===== STREAM AUTH STATE =====
  
  /// Stream untuk mendengarkan perubahan auth state
  Stream<AuthState> get authStateStream;
  
  /// Get current auth state
  AuthState get currentAuthState;
  
  /// Get current user
  AppUser? get currentUser;
  
  // ===== AUTHENTICATION METHODS =====
  
  /// Login dengan email dan password
  Future<AuthState> signInWithEmail({
    required String email,
    required String password,
  });
  
  /// Register dengan email dan password
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
    String? sppgId,
    String? sppgName,
  });
  
  /// Login sebagai anonymous
  Future<AuthState> signInAnonymously();
  
  /// Logout
  Future<AuthState> signOut();
  
  /// Refresh token
  Future<AuthState> refreshToken();
  
  // ===== PASSWORD RECOVERY =====
  
  /// Reset password
  Future<bool> resetPassword({
    required String email,
  });
  
  /// Update password
  Future<bool> updatePassword({
    required String currentPassword,
    required String newPassword,
  });
  
  // ===== USER MANAGEMENT =====
  
  /// Update user profile
  Future<AuthState> updateProfile({
    String? nama,
    String? email,
    Map<String, dynamic>? metadata,
  });
  
  /// Get user profile dari database
  Future<AppUser?> getUserProfile(String userId);
  
  /// Update user profile ke database
  Future<bool> saveUserProfile(AppUser user);
  
  // ===== SESSION MANAGEMENT =====
  
  /// Check apakah session masih valid
  Future<bool> isSessionValid();
  
  /// Get session info
  Future<Map<String, dynamic>?> getSessionInfo();
  
  /// Extend anonymous session
  Future<bool> extendAnonymousSession();
  
  // ===== OFFLINE SUPPORT =====
  
  /// Cache user data untuk offline
  Future<void> cacheUserData(AppUser user);
  
  /// Get cached user data
  Future<AppUser?> getCachedUserData();
  
  /// Clear cache
  Future<void> clearCache();
  
  // ===== UTILITIES =====
  
  /// Validate email format
  bool isEmailValid(String email);
  
  /// Validate password strength
  bool isPasswordValid(String password);
  
  /// Get password requirements
  Map<String, dynamic> getPasswordRequirements();
  
  /// Dispose repository
  void dispose();
}
