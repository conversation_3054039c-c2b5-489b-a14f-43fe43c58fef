import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/responsive_layout.dart';
import '../../../../app/config/app_router.dart';
import '../../../../core/auth/presentation/auth_widgets.dart';
import '../../../../core/auth/domain/auth_state.dart';

/// Demo page untuk menunjukkan integrasi autentikasi Supabase
/// Menampilkan berbagai state auth dan fitur yang tersedia
class AuthIntegrationDemo extends StatefulWidget {
  const AuthIntegrationDemo({super.key});

  @override
  State<AuthIntegrationDemo> createState() => _AuthIntegrationDemoState();
}

class _AuthIntegrationDemoState extends State<AuthIntegrationDemo> 
    with AuthMixin<AuthIntegrationDemo> {
  final Logger _logger = Logger();
  
  @override
  Widget build(BuildContext context) {
    return AuthBuilder(
      builder: (context, authState) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Auth Integration Demo'),
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.textOnPrimary,
          ),
          body: ResponsiveLayout(
            mobile: _buildMobileLayout(authState),
            tablet: _buildTabletLayout(authState),
            desktop: _buildDesktopLayout(authState),
          ),
        );
      },
    );
  }
  
  Widget _buildMobileLayout(AuthState authState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAuthStatusCard(authState),
          const SizedBox(height: AppSpacing.md),
          _buildUserInfoCard(),
          const SizedBox(height: AppSpacing.md),
          _buildActionsCard(),
          const SizedBox(height: AppSpacing.md),
          _buildDebugInfoCard(authState),
        ],
      ),
    );
  }
  
  Widget _buildTabletLayout(AuthState authState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  children: [
                    _buildAuthStatusCard(authState),
                    const SizedBox(height: AppSpacing.md),
                    _buildUserInfoCard(),
                  ],
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  children: [
                    _buildActionsCard(),
                    const SizedBox(height: AppSpacing.md),
                    _buildDebugInfoCard(authState),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildDesktopLayout(AuthState authState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Column(
              children: [
                _buildAuthStatusCard(authState),
                const SizedBox(height: AppSpacing.lg),
                _buildUserInfoCard(),
              ],
            ),
          ),
          const SizedBox(width: AppSpacing.lg),
          Expanded(
            flex: 2,
            child: Column(
              children: [
                _buildActionsCard(),
                const SizedBox(height: AppSpacing.lg),
                _buildDebugInfoCard(authState),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAuthStatusCard(AuthState authState) {
    return AppCardFactory.header(
      title: 'Authentication Status',
      leading: const Icon(AppIcons.security, color: AppColors.primary),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusItem('State', authState.runtimeType.toString()),
          _buildStatusItem('Is Logged In', isLoggedIn.toString()),
          _buildStatusItem('Is Anonymous', isAnonymous.toString()),
          if (authState is AuthenticatedState)
            _buildStatusItem('Is First Login', authState.isFirstLogin.toString()),
          if (authState is AuthErrorState) ...[
            _buildStatusItem('Error', authState.error),
            _buildStatusItem('Can Retry', authState.canRetry.toString()),
          ],
          if (authState is AuthLoadingState && authState.message != null)
            _buildStatusItem('Loading Message', authState.message!),
        ],
      ),
    );
  }
  
  Widget _buildUserInfoCard() {
    final user = currentUser;
    
    return AppCardFactory.header(
      title: 'User Information',
      leading: const Icon(AppIcons.user, color: AppColors.primary),
      child: user != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatusItem('ID', user.id),
                _buildStatusItem('Name', user.displayName),
                _buildStatusItem('Email', user.email ?? 'N/A'),
                _buildStatusItem('Role', user.roleDisplayName),
                _buildStatusItem('SPPG', user.sppgName ?? 'N/A'),
                _buildStatusItem('Anonymous', user.isAnonymous.toString()),
                _buildStatusItem('Created', user.createdAt?.toString() ?? 'N/A'),
                _buildStatusItem('Last Login', user.lastLoginAt?.toString() ?? 'N/A'),
              ],
            )
          : const Text(
              'No user data available',
              style: AppTypography.bodyMedium,
            ),
    );
  }
  
  Widget _buildActionsCard() {
    return AppCardFactory.header(
      title: 'Authentication Actions',
      leading: const Icon(AppIcons.settings, color: AppColors.primary),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (!isLoggedIn) ...[
            AppButtonFactory.primary(
              text: 'Go to Login',
              onPressed: () {
                AppRouter.goToLogin(context);
              },
            ),
            const SizedBox(height: AppSpacing.sm),
            AppButtonFactory.secondary(
              text: 'Login as Guest',
              onPressed: _handleGuestLogin,
            ),
          ] else ...[
            AppButtonFactory.primary(
              text: 'Go to Dashboard',
              onPressed: () {
                AppRouter.goToDashboard(context);
              },
            ),
            const SizedBox(height: AppSpacing.sm),
            AppButtonFactory.secondary(
              text: 'Sign Out',
              onPressed: _handleSignOut,
            ),
          ],
          const SizedBox(height: AppSpacing.sm),
          AppButtonFactory.outline(
            text: 'Refresh Auth State',
            onPressed: _handleRefreshAuth,
          ),
        ],
      ),
    );
  }
  
  Widget _buildDebugInfoCard(AuthState authState) {
    return AppCardFactory.header(
      title: 'Debug Information',
      leading: const Icon(AppIcons.info, color: AppColors.primary),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusItem('Auth Service Initialized', authService.isInitialized.toString()),
          _buildStatusItem('Current Route', ModalRoute.of(context)?.settings.name ?? 'Unknown'),
          _buildStatusItem('Screen Size', '${MediaQuery.of(context).size.width} x ${MediaQuery.of(context).size.height}'),
          _buildStatusItem('Platform', Theme.of(context).platform.name),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Auth State Details:',
            style: AppTypography.labelMedium.copyWith(
              fontWeight: AppTypography.semiBold,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: AppColors.neutralGray100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              authState.toString(),
              style: AppTypography.bodySmall.copyWith(
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTypography.labelMedium.copyWith(
                fontWeight: AppTypography.semiBold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  // ===== EVENT HANDLERS =====
  
  Future<void> _handleGuestLogin() async {
    _logger.d('Handling guest login');
    
    try {
      final result = await authService.signInAnonymously();
      
      if (result is AuthErrorState) {
        _showErrorSnackBar('Guest login failed: ${result.error}');
      } else if (result is AnonymousState) {
        _showSuccessSnackBar('Guest login successful');
      }
    } catch (e) {
      _logger.e('Guest login error: $e');
      _showErrorSnackBar('Guest login failed: $e');
    }
  }
  
  Future<void> _handleSignOut() async {
    _logger.d('Handling sign out');
    
    try {
      final result = await authService.signOut();
      
      if (result is AuthErrorState) {
        _showErrorSnackBar('Sign out failed: ${result.error}');
      } else if (result is UnauthenticatedState) {
        _showSuccessSnackBar('Signed out successfully');
      }
    } catch (e) {
      _logger.e('Sign out error: $e');
      _showErrorSnackBar('Sign out failed: $e');
    }
  }
  
  Future<void> _handleRefreshAuth() async {
    _logger.d('Handling refresh auth');
    
    try {
      final result = await authService.refreshToken();
      
      if (result is AuthErrorState) {
        _showErrorSnackBar('Refresh failed: ${result.error}');
      } else {
        _showSuccessSnackBar('Auth state refreshed');
      }
    } catch (e) {
      _logger.e('Refresh auth error: $e');
      _showErrorSnackBar('Refresh failed: $e');
    }
  }
  
  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.successGreen,
        ),
      );
    }
  }
  
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.errorRed,
        ),
      );
    }
  }
  
  // ===== AUTH MIXIN OVERRIDES =====
  
  @override
  void onUserSignedIn(user) {
    _logger.i('User signed in: ${user.displayName}');
    _showSuccessSnackBar('Welcome, ${user.displayName}!');
  }
  
  @override
  void onUserSignedInAnonymously(user) {
    _logger.i('User signed in anonymously: ${user.displayName}');
    _showSuccessSnackBar('Welcome, Guest!');
  }
  
  @override
  void onUserSignedOut() {
    _logger.i('User signed out');
    _showSuccessSnackBar('Goodbye!');
  }
  
  @override
  void onAuthError(String error) {
    _logger.e('Auth error: $error');
    _showErrorSnackBar('Authentication error: $error');
  }
  
  @override
  void onSessionExpired() {
    _logger.w('Session expired');
    _showErrorSnackBar('Session expired. Please login again.');
  }
}
