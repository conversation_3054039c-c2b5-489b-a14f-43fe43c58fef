import 'dart:async';
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:flutter_secure_storage/flutter_secure_storage.dart'; // Temporarily disabled due to Windows build issues
import 'package:email_validator/email_validator.dart';
import 'package:logger/logger.dart';

import '../domain/auth_repository.dart';
import '../domain/app_user.dart';
import '../domain/auth_state.dart';
import '../../config/supabase_service.dart';
import '../../config/supabase_config.dart';

/// Implementasi AuthRepository menggunakan Supabase
class SupabaseAuthRepository implements AuthRepository {
  final Logger _logger = Logger();
  
  // Services
  final SupabaseService _supabaseService;
  // final FlutterSecureStorage _secureStorage; // Temporarily disabled due to Windows build issues
  SharedPreferences? _sharedPreferences;
  
  // Stream controllers
  final StreamController<AuthState> _authStateController = StreamController<AuthState>.broadcast();
  
  // Current state
  AuthState _currentAuthState = const AuthInitialState();
  StreamSubscription<AuthState>? _authStateSubscription;
  
  // Cache
  AppUser? _cachedUser;
  
  // Constructor
  SupabaseAuthRepository({
    SupabaseService? supabaseService,
    // FlutterSecureStorage? secureStorage, // Temporarily disabled due to Windows build issues
  }) : _supabaseService = supabaseService ?? SupabaseService.instance;
  
  // ===== INITIALIZATION =====
  
  /// Initialize repository
  Future<void> initialize() async {
    try {
      _logger.i('Initializing SupabaseAuthRepository');
      
      // Initialize shared preferences
      _sharedPreferences = await SharedPreferences.getInstance();
      
      // Check if Supabase is initialized
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service is not initialized');
      }
      
      // Setup auth state listener
      _setupAuthStateListener();
      
      // Check for existing session
      await _checkExistingSession();
      
      _logger.i('SupabaseAuthRepository initialized successfully');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize SupabaseAuthRepository: $e', stackTrace: stackTrace);
      _emitState(AuthErrorState(error: 'Failed to initialize authentication: $e'));
      rethrow;
    }
  }
  
  /// Setup listener untuk auth state changes dari Supabase
  void _setupAuthStateListener() {
    _logger.d('Setting up auth state listener');
    
    _supabaseService.auth.onAuthStateChange.listen((data) async {
      final event = data.event;
      final session = data.session;
      
      _logger.d('Supabase auth state changed: $event');
      
      try {
        switch (event) {
          case AuthChangeEvent.signedIn:
            await _handleSignedIn(session);
            break;
          case AuthChangeEvent.signedOut:
            await _handleSignedOut();
            break;
          case AuthChangeEvent.tokenRefreshed:
            await _handleTokenRefreshed(session);
            break;
          case AuthChangeEvent.userUpdated:
            await _handleUserUpdated(session);
            break;
          case AuthChangeEvent.passwordRecovery:
            _logger.d('Password recovery initiated');
            break;
          default:
            _logger.d('Unhandled auth event: $event');
        }
      } catch (e, stackTrace) {
        _logger.e('Error handling auth state change: $e', stackTrace: stackTrace);
        _emitState(AuthErrorState(error: 'Authentication error: $e'));
      }
    });
  }
  
  /// Check for existing session
  Future<void> _checkExistingSession() async {
    _logger.d('Checking for existing session');
    
    try {
      final session = _supabaseService.currentSession;
      if (session != null) {
        _logger.d('Found existing session');
        await _handleSignedIn(session);
      } else {
        _logger.d('No existing session found');
        _emitState(const UnauthenticatedState());
      }
    } catch (e, stackTrace) {
      _logger.e('Error checking existing session: $e', stackTrace: stackTrace);
      _emitState(const UnauthenticatedState(message: 'Failed to check existing session'));
    }
  }
  
  // ===== AUTH STATE HANDLERS =====
  
  /// Handle signed in event
  Future<void> _handleSignedIn(Session? session) async {
    if (session?.user == null) {
      _logger.w('Signed in but no user in session');
      return;
    }
    
    final supabaseUser = session!.user;
    _logger.i('User signed in: ${supabaseUser.id}');
    
    try {
      // Get user profile from database
      final appUser = await _getUserFromSupabase(supabaseUser.id);
      
      if (appUser != null) {
        _cachedUser = appUser;
        await cacheUserData(appUser);
        
        if (appUser.isAnonymous) {
          _emitState(AnonymousState(user: appUser));
        } else {
          _emitState(AuthenticatedState(user: appUser));
        }
      } else {
        // Create user profile if not exists
        final newUser = _createUserFromSupabaseUser(supabaseUser);
        await _saveUserToSupabase(newUser);
        
        _cachedUser = newUser;
        await cacheUserData(newUser);
        
        if (newUser.isAnonymous) {
          _emitState(AnonymousState(user: newUser));
        } else {
          _emitState(AuthenticatedState(user: newUser, isFirstLogin: true));
        }
      }
    } catch (e, stackTrace) {
      _logger.e('Error handling signed in: $e', stackTrace: stackTrace);
      _emitState(AuthErrorState(error: 'Failed to load user profile: $e'));
    }
  }
  
  /// Handle signed out event
  Future<void> _handleSignedOut() async {
    _logger.i('User signed out');
    
    try {
      _cachedUser = null;
      await clearCache();
      _emitState(const UnauthenticatedState());
    } catch (e, stackTrace) {
      _logger.e('Error handling signed out: $e', stackTrace: stackTrace);
      _emitState(const UnauthenticatedState(message: 'Signed out with errors'));
    }
  }
  
  /// Handle token refreshed event
  Future<void> _handleTokenRefreshed(Session? session) async {
    _logger.d('Token refreshed');
    
    if (session?.user != null && _cachedUser != null) {
      // Update last login time
      final updatedUser = _cachedUser!.copyWith(
        lastLoginAt: DateTime.now(),
      );
      _cachedUser = updatedUser;
      await cacheUserData(updatedUser);
    }
  }
  
  /// Handle user updated event
  Future<void> _handleUserUpdated(Session? session) async {
    _logger.d('User updated');
    
    if (session?.user != null) {
      try {
        final appUser = await _getUserFromSupabase(session!.user.id);
        if (appUser != null) {
          _cachedUser = appUser;
          await cacheUserData(appUser);
          
          if (appUser.isAnonymous) {
            _emitState(AnonymousState(user: appUser));
          } else {
            _emitState(AuthenticatedState(user: appUser));
          }
        }
      } catch (e, stackTrace) {
        _logger.e('Error handling user updated: $e', stackTrace: stackTrace);
      }
    }
  }
  
  // ===== STREAM METHODS =====
  
  @override
  Stream<AuthState> get authStateStream => _authStateController.stream;
  
  @override
  AuthState get currentAuthState => _currentAuthState;
  
  @override
  AppUser? get currentUser => _cachedUser;
  
  /// Emit new auth state
  void _emitState(AuthState state) {
    _currentAuthState = state;
    _authStateController.add(state);
    _logger.d('Auth state emitted: ${state.runtimeType}');
  }
  
  // ===== AUTHENTICATION METHODS =====
  
  @override
  Future<AuthState> signInWithEmail({
    required String email,
    required String password,
  }) async {
    _logger.i('Attempting to sign in with email: $email');
    
    try {
      _emitState(const AuthLoadingState(message: 'Signing in...'));
      
      // Validate input
      if (!isEmailValid(email)) {
        throw Exception('Invalid email format');
      }
      
      if (!isPasswordValid(password)) {
        throw Exception('Password does not meet requirements');
      }
      
      // Sign in dengan Supabase
      final response = await _supabaseService.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user == null) {
        throw Exception('Sign in failed: No user returned');
      }
      
      _logger.i('Sign in successful for user: ${response.user!.id}');
      
      // State akan di-emit oleh listener
      return _currentAuthState;
      
    } catch (e, stackTrace) {
      _logger.e('Sign in failed: $e', stackTrace: stackTrace);
      final errorState = AuthErrorState(error: _getErrorMessage(e));
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
    String? sppgId,
    String? sppgName,
  }) async {
    _logger.i('Attempting to sign up with email: $email');
    
    try {
      _emitState(const AuthLoadingState(message: 'Creating account...'));
      
      // Validate input
      if (!isEmailValid(email)) {
        throw Exception('Invalid email format');
      }
      
      if (!isPasswordValid(password)) {
        throw Exception('Password does not meet requirements');
      }
      
      if (nama.trim().isEmpty) {
        throw Exception('Name is required');
      }
      
      if (role.trim().isEmpty) {
        throw Exception('Role is required');
      }
      
      // Special handling for admin yayasan registration
      if (role == 'admin_yayasan') {
        if (sppgName == null || sppgName.trim().isEmpty) {
          throw Exception('Nama yayasan is required for admin yayasan');
        }
        
        // Use Edge Function for admin yayasan registration
        final response = await _supabaseService.functions.invoke(
          'register-admin-yayasan',
          body: {
            'email': email,
            'password': password,
            'nama': nama,
            'namaYayasan': sppgName,
          },
        );
        
        if (response.data == null || response.data['success'] != true) {
          throw Exception(response.data?['error'] ?? 'Registration failed');
        }
        
        _logger.i('Admin yayasan registration successful: ${response.data}');
        
        // Sign in the newly created user
        final signInResponse = await _supabaseService.auth.signInWithPassword(
          email: email,
          password: password,
        );
        
        if (signInResponse.user == null) {
          throw Exception('Sign in failed after registration');
        }
        
        _logger.i('Auto sign in successful for admin yayasan: ${signInResponse.user!.id}');
        
        // State akan di-emit oleh listener
        return _currentAuthState;
        
      } else {
        // Standard registration for other roles
        final response = await _supabaseService.auth.signUp(
          email: email,
          password: password,
          data: {
            'nama': nama,
            'role': role,
            'sppg_id': sppgId,
            'sppg_name': sppgName,
          },
        );
        
        if (response.user == null) {
          throw Exception('Sign up failed: No user returned');
        }
        
        _logger.i('Sign up successful for user: ${response.user!.id}');
        
        // State akan di-emit oleh listener
        return _currentAuthState;
      }
      
    } catch (e, stackTrace) {
      _logger.e('Sign up failed: $e', stackTrace: stackTrace);
      final errorState = AuthErrorState(error: _getErrorMessage(e));
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> signInAnonymously() async {
    _logger.i('Attempting to sign in anonymously');
    
    try {
      _emitState(const AuthLoadingState(message: 'Signing in as guest...'));
      
      // Check if anonymous auth is allowed
      if (!SupabaseConfig.allowAnonymousAuth) {
        throw Exception('Anonymous authentication is not allowed');
      }
      
      // Sign in anonymously dengan Supabase
      final response = await _supabaseService.auth.signInAnonymously();
      
      if (response.user == null) {
        throw Exception('Anonymous sign in failed: No user returned');
      }
      
      _logger.i('Anonymous sign in successful for user: ${response.user!.id}');
      
      // State akan di-emit oleh listener
      return _currentAuthState;
      
    } catch (e, stackTrace) {
      _logger.e('Anonymous sign in failed: $e', stackTrace: stackTrace);
      final errorState = AuthErrorState(error: _getErrorMessage(e));
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> signOut() async {
    _logger.i('Attempting to sign out');
    
    try {
      _emitState(const LoggingOutState());
      
      // Sign out dari Supabase
      await _supabaseService.auth.signOut();
      
      _logger.i('Sign out successful');
      
      // State akan di-emit oleh listener
      return _currentAuthState;
      
    } catch (e, stackTrace) {
      _logger.e('Sign out failed: $e', stackTrace: stackTrace);
      final errorState = AuthErrorState(error: _getErrorMessage(e));
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AuthState> refreshToken() async {
    _logger.d('Attempting to refresh token');
    
    try {
      _emitState(const AuthLoadingState(message: 'Refreshing session...'));
      
      // Refresh token dengan Supabase
      final response = await _supabaseService.auth.refreshSession();
      
      if (response.session == null) {
        throw Exception('Token refresh failed: No session returned');
      }
      
      _logger.d('Token refresh successful');
      
      // State akan di-emit oleh listener
      return _currentAuthState;
      
    } catch (e, stackTrace) {
      _logger.e('Token refresh failed: $e', stackTrace: stackTrace);
      final errorState = AuthErrorState(error: _getErrorMessage(e));
      _emitState(errorState);
      return errorState;
    }
  }
  
  // ===== PASSWORD RECOVERY =====
  
  @override
  Future<bool> resetPassword({required String email}) async {
    _logger.i('Attempting to reset password for email: $email');
    
    try {
      if (!isEmailValid(email)) {
        throw Exception('Invalid email format');
      }
      
      await _supabaseService.auth.resetPasswordForEmail(email);
      
      _logger.i('Password reset email sent successfully');
      return true;
      
    } catch (e, stackTrace) {
      _logger.e('Password reset failed: $e', stackTrace: stackTrace);
      return false;
    }
  }
  
  @override
  Future<bool> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _logger.i('Attempting to update password');
    
    try {
      if (!isPasswordValid(newPassword)) {
        throw Exception('New password does not meet requirements');
      }
      
      // Update password dengan Supabase
      final response = await _supabaseService.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      
      if (response.user == null) {
        throw Exception('Password update failed');
      }
      
      _logger.i('Password updated successfully');
      return true;
      
    } catch (e, stackTrace) {
      _logger.e('Password update failed: $e', stackTrace: stackTrace);
      return false;
    }
  }
  
  // ===== USER MANAGEMENT =====
  
  @override
  Future<AuthState> updateProfile({
    String? nama,
    String? email,
    Map<String, dynamic>? metadata,
  }) async {
    _logger.i('Attempting to update profile');
    
    try {
      _emitState(const AuthLoadingState(message: 'Updating profile...'));
      
      if (_cachedUser == null) {
        throw Exception('No user logged in');
      }
      
      // Update user di Supabase Auth
      final userAttributes = UserAttributes(
        email: email,
        data: {
          'nama': nama ?? _cachedUser!.nama,
          'role': _cachedUser!.role,
          'sppg_id': _cachedUser!.sppgId,
          'sppg_name': _cachedUser!.sppgName,
          if (metadata != null) ...metadata,
        },
      );
      
      final response = await _supabaseService.auth.updateUser(userAttributes);
      
      if (response.user == null) {
        throw Exception('Profile update failed');
      }
      
      // Update user di database
      final updatedUser = _cachedUser!.copyWith(
        nama: nama ?? _cachedUser!.nama,
        email: email ?? _cachedUser!.email,
        metadata: metadata ?? _cachedUser!.metadata,
      );
      
      await _saveUserToSupabase(updatedUser);
      
      _cachedUser = updatedUser;
      await cacheUserData(updatedUser);
      
      _logger.i('Profile updated successfully');
      
      // State akan di-emit oleh listener
      return _currentAuthState;
      
    } catch (e, stackTrace) {
      _logger.e('Profile update failed: $e', stackTrace: stackTrace);
      final errorState = AuthErrorState(error: _getErrorMessage(e));
      _emitState(errorState);
      return errorState;
    }
  }
  
  @override
  Future<AppUser?> getUserProfile(String userId) async {
    return await _getUserFromSupabase(userId);
  }
  
  @override
  Future<bool> saveUserProfile(AppUser user) async {
    return await _saveUserToSupabase(user);
  }
  
  // ===== DATABASE METHODS =====
  
  /// Get user dari database Supabase
  Future<AppUser?> _getUserFromSupabase(String userId) async {
    try {
      final response = await _supabaseService.client
          .from('users')
          .select()
          .eq('id', userId)
          .single();
      
      return AppUser.fromMap(response);
      
    } catch (e) {
      _logger.e('Failed to get user from database: $e');
      return null;
    }
  }
  
  /// Save user ke database Supabase
  Future<bool> _saveUserToSupabase(AppUser user) async {
    try {
      await _supabaseService.client
          .from('users')
          .upsert(user.toMap());
      
      return true;
      
    } catch (e) {
      _logger.e('Failed to save user to database: $e');
      return false;
    }
  }
  
  /// Create AppUser dari Supabase User
  AppUser _createUserFromSupabaseUser(User supabaseUser) {
    final userData = supabaseUser.userMetadata ?? {};
    
    return AppUser(
      id: supabaseUser.id,
      email: supabaseUser.email,
      nama: userData['nama'] ?? supabaseUser.userMetadata?['full_name'],
      role: userData['role'] ?? (supabaseUser.isAnonymous ? 'guest' : 'kepala_dapur'),
      sppgId: userData['sppg_id'],
      sppgName: userData['sppg_name'],
      isAnonymous: supabaseUser.isAnonymous,
      createdAt: DateTime.tryParse(supabaseUser.createdAt),
      lastLoginAt: DateTime.now(),
      metadata: userData,
    );
  }
  
  // ===== SESSION MANAGEMENT =====
  
  @override
  Future<bool> isSessionValid() async {
    try {
      final session = _supabaseService.currentSession;
      if (session == null) return false;
      
      // Check if session is expired
      final expiresAt = session.expiresAt;
      if (expiresAt != null && expiresAt <= DateTime.now().millisecondsSinceEpoch ~/ 1000) {
        return false;
      }
      
      return true;
      
    } catch (e) {
      _logger.e('Error checking session validity: $e');
      return false;
    }
  }
  
  @override
  Future<Map<String, dynamic>?> getSessionInfo() async {
    try {
      final session = _supabaseService.currentSession;
      if (session == null) return null;
      
      return {
        'user_id': session.user.id,
        'access_token': session.accessToken,
        'refresh_token': session.refreshToken,
        'expires_at': session.expiresAt,
        'expires_in': session.expiresIn,
        'token_type': session.tokenType,
      };
      
    } catch (e) {
      _logger.e('Error getting session info: $e');
      return null;
    }
  }
  
  @override
  Future<bool> extendAnonymousSession() async {
    try {
      if (!_supabaseService.isAnonymous) return false;
      
      // Untuk anonymous session, kita bisa refresh token
      final response = await _supabaseService.auth.refreshSession();
      return response.session != null;
      
    } catch (e) {
      _logger.e('Error extending anonymous session: $e');
      return false;
    }
  }
  
  // ===== OFFLINE SUPPORT =====
  
  @override
  Future<void> cacheUserData(AppUser user) async {
    try {
      // Use SharedPreferences instead of FlutterSecureStorage for now
      await _sharedPreferences?.setString(
        'cached_user',
        json.encode(user.toJson()),
      );
      
      await _sharedPreferences?.setString(
        'cached_user_id',
        user.id,
      );
      
      _logger.d('User data cached successfully');
      
    } catch (e) {
      _logger.e('Failed to cache user data: $e');
    }
  }
  
  @override
  Future<AppUser?> getCachedUserData() async {
    try {
      final userData = _sharedPreferences?.getString('cached_user');
      if (userData == null) return null;
      
      final userMap = json.decode(userData) as Map<String, dynamic>;
      return AppUser.fromJson(userMap);
      
    } catch (e) {
      _logger.e('Failed to get cached user data: $e');
      return null;
    }
  }
  
  @override
  Future<void> clearCache() async {
    try {
      await _sharedPreferences?.remove('cached_user');
      await _sharedPreferences?.remove('cached_user_id');
      
      _logger.d('Cache cleared successfully');
      
    } catch (e) {
      _logger.e('Failed to clear cache: $e');
    }
  }
  
  // ===== VALIDATION =====
  
  @override
  bool isEmailValid(String email) {
    return EmailValidator.validate(email);
  }
  
  @override
  bool isPasswordValid(String password) {
    // Minimum 8 characters, at least one letter and one number
    return password.length >= 8 && 
           password.contains(RegExp(r'[A-Za-z]')) && 
           password.contains(RegExp(r'[0-9]'));
  }
  
  @override
  Map<String, dynamic> getPasswordRequirements() {
    return {
      'minLength': 8,
      'requireLetter': true,
      'requireNumber': true,
      'requireSpecialChar': false,
      'requireUppercase': false,
    };
  }
  
  // ===== UTILITIES =====
  
  /// Get user-friendly error message
  String _getErrorMessage(dynamic error) {
    if (error is AuthException) {
      switch (error.statusCode) {
        case '400':
          return 'Invalid request. Please check your input.';
        case '401':
          return 'Invalid credentials. Please try again.';
        case '403':
          return 'Access forbidden. Please check your permissions.';
        case '422':
          return 'Invalid data format. Please check your input.';
        case '429':
          return 'Too many requests. Please wait a moment and try again.';
        default:
          return error.message;
      }
    }
    
    return error.toString();
  }
  
  // ===== DISPOSAL =====
  
  @override
  void dispose() {
    _logger.d('Disposing SupabaseAuthRepository');
    _authStateController.close();
    _authStateSubscription?.cancel();
    _supabaseService.dispose();
  }
}
