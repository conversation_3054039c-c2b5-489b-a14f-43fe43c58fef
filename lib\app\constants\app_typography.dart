import 'package:flutter/material.dart';

/// Typography system untuk Aplikasi SOD-MBG
/// Menggunakan hierarki yang jelas dan mudah dibaca untuk lingkungan dapur
class AppTypography {
  // Private constructor
  AppTypography._();

  // ===== FONT FAMILIES =====
  /// Font utama - system font untuk performa optimal
  static const String primaryFontFamily = 'Roboto';
  static const String secondaryFontFamily = 'Roboto';

  // ===== FONT WEIGHTS =====
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;

  // ===== FONT SIZES =====
  static const double fontSize10 = 10.0;
  static const double fontSize12 = 12.0;
  static const double fontSize14 = 14.0;
  static const double fontSize16 = 16.0;
  static const double fontSize18 = 18.0;
  static const double fontSize20 = 20.0;
  static const double fontSize24 = 24.0;
  static const double fontSize28 = 28.0;
  static const double fontSize32 = 32.0;
  static const double fontSize36 = 36.0;
  static const double fontSize40 = 40.0;
  static const double fontSize48 = 48.0;

  // ===== LINE HEIGHTS =====
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.4;
  static const double lineHeightLoose = 1.6;

  // ===== HEADING STYLES =====
  /// H1 - Untuk judul utama aplikasi
  static const TextStyle h1 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize48,
    fontWeight: bold,
    height: lineHeightTight,
    letterSpacing: -0.5,
  );

  /// H2 - Untuk judul halaman utama
  static const TextStyle h2 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize40,
    fontWeight: bold,
    height: lineHeightTight,
    letterSpacing: -0.25,
  );

  /// H3 - Untuk judul section
  static const TextStyle h3 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize32,
    fontWeight: semiBold,
    height: lineHeightTight,
    letterSpacing: 0,
  );

  /// H4 - Untuk sub judul
  static const TextStyle h4 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize28,
    fontWeight: semiBold,
    height: lineHeightNormal,
    letterSpacing: 0.25,
  );

  /// H5 - Untuk judul card atau komponen
  static const TextStyle h5 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize24,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0,
  );

  /// H6 - Untuk judul kecil
  static const TextStyle h6 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize20,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.15,
  );

  // ===== BODY TEXT STYLES =====
  /// Body Large - Untuk konten utama
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize16,
    fontWeight: regular,
    height: lineHeightLoose,
    letterSpacing: 0.5,
  );

  /// Body Medium - Untuk konten standar
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize14,
    fontWeight: regular,
    height: lineHeightLoose,
    letterSpacing: 0.25,
  );

  /// Body Small - Untuk konten pendukung
  static const TextStyle bodySmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize12,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: 0.4,
  );

  // ===== LEGACY ALIASES FOR BACKWARD COMPATIBILITY =====
  /// Alias untuk backward compatibility - gunakan bodyLarge
  static const TextStyle body1 = bodyLarge;
  
  /// Alias untuk backward compatibility - gunakan bodyMedium
  static const TextStyle body2 = bodyMedium;

  // ===== LABEL STYLES =====
  /// Label Large - Untuk button dan tab
  static const TextStyle labelLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize14,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.1,
  );

  /// Label Medium - Untuk form label
  static const TextStyle labelMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize12,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.5,
  );

  /// Label Small - Untuk caption dan metadata
  static const TextStyle labelSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize10,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.5,
  );

  /// Caption - Alias untuk labelSmall untuk backward compatibility
  static const TextStyle caption = labelSmall;

  // ===== SPECIAL KITCHEN TYPOGRAPHY =====
  /// Kitchen Display - Untuk tampilan operasional dapur yang mudah dibaca dari jauh
  static const TextStyle kitchenDisplay = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize36,
    fontWeight: bold,
    height: lineHeightTight,
    letterSpacing: 1.0,
  );

  /// Kitchen Status - Untuk status operasional
  static const TextStyle kitchenStatus = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize20,
    fontWeight: semiBold,
    height: lineHeightNormal,
    letterSpacing: 0.5,
  );

  /// Kitchen Counter - Untuk counter dan angka penting
  static const TextStyle kitchenCounter = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize28,
    fontWeight: bold,
    height: lineHeightTight,
    letterSpacing: 0,
  );

  // ===== FORM TYPOGRAPHY =====
  /// Input Text - Untuk input field
  static const TextStyle inputText = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize16,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: 0.15,
  );

  /// Hint Text - Untuk placeholder
  static const TextStyle hintText = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize16,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: 0.15,
  );

  /// Error Text - Untuk pesan error
  static const TextStyle errorText = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize12,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: 0.4,
  );

  // ===== BUTTON TYPOGRAPHY =====
  /// Button Large - Untuk primary action
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize16,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.5,
  );

  /// Button Medium - Untuk secondary action
  static const TextStyle buttonMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize14,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.25,
  );

  /// Button Small - Untuk tertiary action
  static const TextStyle buttonSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize12,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.5,
  );

  // ===== NAVIGATION TYPOGRAPHY =====
  /// Navigation Item - Untuk menu navigation
  static const TextStyle navigationItem = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize14,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.25,
  );

  /// Tab Item - Untuk tab bar
  static const TextStyle tabItem = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: fontSize14,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: 0.1,
  );

  // ===== UTILITY METHODS =====
  /// Mengembalikan TextStyle dengan warna tertentu
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  /// Mengembalikan TextStyle dengan ukuran tertentu
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }

  /// Mengembalikan TextStyle dengan weight tertentu
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }
}
