import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';
import 'app_button.dart';

/// Komponen untuk berbagai state dalam aplikasi
/// Menyediakan UI yang konsisten untuk loading, empty, dan error states
class AppStateComponents {
  AppStateComponents._();
}

/// Widget untuk loading state
class AppLoadingState extends StatelessWidget {
  final String? message;
  final bool isFullScreen;
  final Color? color;

  const AppLoadingState({
    super.key,
    this.message,
    this.isFullScreen = false,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final loadingWidget = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(
            color ?? AppColors.primary,
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: AppSpacing.md),
          Text(
            message!,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    if (isFullScreen) {
      return Scaffold(
        body: Center(child: loadingWidget),
      );
    }

    return Center(child: loadingWidget);
  }
}

/// Widget untuk empty state
class AppEmptyState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? illustration;
  final List<Widget>? actions;
  final bool isFullScreen;

  const AppEmptyState({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.illustration,
    this.actions,
    this.isFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    final emptyWidget = Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon atau illustration
          if (illustration != null)
            illustration!
          else if (icon != null)
            Icon(
              icon!,
              size: 80,
              color: AppColors.neutralGray400,
            )
          else
            const Icon(
              AppIcons.inventory,
              size: 80,
              color: AppColors.neutralGray400,
            ),
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            title,
            style: AppTypography.h5.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              subtitle!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          // Actions
          if (actions != null) ...[
            const SizedBox(height: AppSpacing.lg),
            ...actions!,
          ],
        ],
      ),
    );

    if (isFullScreen) {
      return Scaffold(
        body: Center(child: emptyWidget),
      );
    }

    return Center(child: emptyWidget);
  }
}

/// Widget untuk error state
class AppErrorState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final VoidCallback? onRetry;
  final String? retryText;
  final bool isFullScreen;

  const AppErrorState({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.onRetry,
    this.retryText,
    this.isFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    final errorWidget = Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error icon
          Icon(
            icon ?? AppIcons.error,
            size: 80,
            color: AppColors.errorRed,
          ),
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            title,
            style: AppTypography.h5.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              subtitle!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          // Retry button
          if (onRetry != null) ...[
            const SizedBox(height: AppSpacing.lg),
            AppButtonFactory.outline(
              text: retryText ?? 'Coba Lagi',
              onPressed: onRetry,
              icon: AppIcons.refresh,
            ),
          ],
        ],
      ),
    );

    if (isFullScreen) {
      return Scaffold(
        body: Center(child: errorWidget),
      );
    }

    return Center(child: errorWidget);
  }
}

/// Widget untuk offline state
class AppOfflineState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onRetry;
  final bool isFullScreen;

  const AppOfflineState({
    super.key,
    this.title = 'Tidak Ada Koneksi',
    this.subtitle,
    this.onRetry,
    this.isFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    final offlineWidget = Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Offline icon
          const Icon(
            AppIcons.offline,
            size: 80,
            color: AppColors.warningYellow,
          ),
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            title,
            style: AppTypography.h5.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              subtitle!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          const SizedBox(height: AppSpacing.md),
          
          // Offline mode info
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColors.kitchenClean,
              borderRadius: BorderRadius.circular(AppRadius.md),
              border: Border.all(
                color: AppColors.successGreen.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  AppIcons.offline,
                  size: AppIcons.sizeSmall,
                  color: AppColors.successGreen,
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  'Mode Offline Aktif',
                  style: AppTypography.labelMedium.copyWith(
                    color: AppColors.successGreen,
                  ),
                ),
              ],
            ),
          ),
          
          // Retry button
          if (onRetry != null) ...[
            const SizedBox(height: AppSpacing.lg),
            AppButtonFactory.outline(
              text: 'Coba Koneksi Lagi',
              onPressed: onRetry,
              icon: AppIcons.refresh,
            ),
          ],
        ],
      ),
    );

    if (isFullScreen) {
      return Scaffold(
        body: Center(child: offlineWidget),
      );
    }

    return Center(child: offlineWidget);
  }
}

/// Widget untuk success state
class AppSuccessState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final List<Widget>? actions;
  final bool isFullScreen;

  const AppSuccessState({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.actions,
    this.isFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    final successWidget = Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success icon
          Icon(
            icon ?? AppIcons.checkmark,
            size: 80,
            color: AppColors.successGreen,
          ),
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            title,
            style: AppTypography.h5.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              subtitle!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          // Actions
          if (actions != null) ...[
            const SizedBox(height: AppSpacing.lg),
            ...actions!,
          ],
        ],
      ),
    );

    if (isFullScreen) {
      return Scaffold(
        body: Center(child: successWidget),
      );
    }

    return Center(child: successWidget);
  }
}

/// Widget untuk maintenance state
class AppMaintenanceState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final DateTime? estimatedTime;
  final bool isFullScreen;

  const AppMaintenanceState({
    super.key,
    this.title = 'Sedang Maintenance',
    this.subtitle,
    this.estimatedTime,
    this.isFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    final maintenanceWidget = Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Maintenance icon
          const Icon(
            AppIcons.settings,
            size: 80,
            color: AppColors.warningYellow,
          ),
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            title,
            style: AppTypography.h5.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              subtitle!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          // Estimated time
          if (estimatedTime != null) ...[
            const SizedBox(height: AppSpacing.md),
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.warningYellow.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppRadius.md),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    AppIcons.timer,
                    size: AppIcons.sizeSmall,
                    color: AppColors.warningYellow,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    'Estimasi selesai: ${_formatTime(estimatedTime!)}',
                    style: AppTypography.labelMedium.copyWith(
                      color: AppColors.warningYellow,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );

    if (isFullScreen) {
      return Scaffold(
        body: Center(child: maintenanceWidget),
      );
    }

    return Center(child: maintenanceWidget);
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = time.difference(now);
    
    if (difference.inHours > 0) {
      return '${difference.inHours} jam ${difference.inMinutes.remainder(60)} menit';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit';
    } else {
      return 'Sebentar lagi';
    }
  }
}

/// Widget untuk shimmer loading
class AppShimmerLoading extends StatefulWidget {
  final Widget child;
  final bool isLoading;

  const AppShimmerLoading({
    super.key,
    required this.child,
    required this.isLoading,
  });

  @override
  State<AppShimmerLoading> createState() => _AppShimmerLoadingState();
}

class _AppShimmerLoadingState extends State<AppShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    _animation = Tween<double>(begin: -1.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.neutralGray200,
                AppColors.neutralGray100,
                AppColors.neutralGray200,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ],
            ),
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// Widget untuk progress state
class AppProgressState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final double progress;
  final String? progressText;
  final bool isFullScreen;

  const AppProgressState({
    super.key,
    required this.title,
    this.subtitle,
    required this.progress,
    this.progressText,
    this.isFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    final progressWidget = Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Progress icon
          const Icon(
            AppIcons.timer,
            size: 80,
            color: AppColors.primary,
          ),
          const SizedBox(height: AppSpacing.lg),
          
          // Title
          Text(
            title,
            style: AppTypography.h5.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              subtitle!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          const SizedBox(height: AppSpacing.xl),
          
          // Progress indicator
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppColors.neutralGray200,
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Progress text
          Text(
            progressText ?? '${(progress * 100).toInt()}%',
            style: AppTypography.labelLarge.copyWith(
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );

    if (isFullScreen) {
      return Scaffold(
        body: Center(child: progressWidget),
      );
    }

    return Center(child: progressWidget);
  }
}
