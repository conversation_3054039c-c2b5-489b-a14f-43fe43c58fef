import 'package:aplikasi_sppg/core/auth/domain/app_user.dart';
import 'package:aplikasi_sppg/core/auth/presentation/cubit/auth_cubit.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/dashboard_summary.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/cubit/dashboard_cubit.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/approval_request_item.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/summary_card.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/task_list_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/sod_navigation_drawer.dart';
import '../../../../app/config/app_router.dart';

/// Halaman dashboard utama untuk Aplikasi SOD-MBG
/// Menampilkan overview operasional dapur dan akses ke semua fitur
class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  final Logger _logger = Logger();

  int _selectedNavIndex = 0;
  final PageController _pageController = PageController();

  final List<DashboardNavItem> _navItems = [
    DashboardNavItem(
      icon: AppIcons.dashboard,
      label: 'Dashboard',
      route: AppRouter.dashboard,
    ),
    DashboardNavItem(
      icon: AppIcons.kitchen,
      label: 'Kitchen',
      route: AppRouter.kitchenManagement,
    ),
    DashboardNavItem(
      icon: AppIcons.inventory,
      label: 'Inventory',
      route: AppRouter.inventory,
    ),
    DashboardNavItem(
      icon: AppIcons.delivery,
      label: 'Logistics',
      route: AppRouter.logistics,
    ),
    DashboardNavItem(
      icon: AppIcons.money,
      label: 'Financial',
      route: AppRouter.financial,
    ),
    DashboardNavItem(
      icon: AppIcons.report,
      label: 'Reports',
      route: AppRouter.reporting,
    ),
  ];

  @override
  void initState() {
    super.initState();
    context.read<DashboardCubit>().getDashboardSummary();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building DashboardPage');

    final screenSize = MediaQuery.of(context).size;
    final isDesktop = AppBreakpoints.isDesktop(screenSize.width);
    final isTablet = AppBreakpoints.isTablet(screenSize.width);

    return Scaffold(
      appBar: _buildAppBar(context),
      drawer: _buildNavigationDrawer(),
      body: Row(
        children: [
          if (isDesktop || isTablet) _buildSideNavigation(),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _selectedNavIndex = index;
                });
              },
              children: [
                _buildDashboardContent(),
                _buildKitchenContent(),
                _buildInventoryContent(),
                _buildLogisticsContent(),
                _buildFinancialContent(),
                _buildReportsContent(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar:
          (isDesktop || isTablet) ? null : _buildBottomNavigation(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Row(
        children: [
          const Icon(
            AppIcons.restaurant,
            color: AppColors.textOnPrimary,
          ),
          const SizedBox(width: AppSpacing.sm),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'SOD-MBG',
                style: AppTypography.h6.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: AppTypography.bold,
                ),
              ),
              Text(
                'Dashboard',
                style: AppTypography.labelSmall.copyWith(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(AppIcons.notification),
          onPressed: _showNotifications,
        ),
        IconButton(
          icon: const Icon(AppIcons.sync),
          onPressed: _syncData,
        ),
        IconButton(
          icon: const Icon(AppIcons.profile),
          onPressed: _showProfile,
        ),
        const SizedBox(width: AppSpacing.sm),
      ],
    );
  }

  Widget _buildNavigationDrawer() {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        if (state is Authenticated) {
          return SodNavigationDrawer(
            userName: state.user.nama ?? '',
            userRole: state.user.role,
            sppgName: state.user.sppgName ?? '',
            selectedIndex: _selectedNavIndex,
            onItemSelected: (index) {
              setState(() {
                _selectedNavIndex = index;
              });
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            onProfileTap: _showProfile,
            onSettingsTap: () => AppRouter.goToUserManagement(context),
            onLogoutTap: _logout,
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSideNavigation() {
    return Container(
      width: 280,
      decoration: const BoxDecoration(
        color: AppColors.backgroundSecondary,
        border: Border(
          right: BorderSide(
            color: AppColors.neutralGray200,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.md),
          _buildUserInfo(),
          const SizedBox(height: AppSpacing.lg),
          Expanded(
            child: ListView.builder(
              itemCount: _navItems.length,
              itemBuilder: (context, index) {
                final item = _navItems[index];
                final isSelected = index == _selectedNavIndex;

                return Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.xs,
                  ),
                  child: ListTile(
                    leading: Icon(
                      item.icon,
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.neutralGray600,
                    ),
                    title: Text(
                      item.label,
                      style: AppTypography.navigationItem.copyWith(
                        color:
                            isSelected ? AppColors.primary : AppColors.textPrimary,
                        fontWeight: isSelected
                            ? AppTypography.semiBold
                            : AppTypography.regular,
                      ),
                    ),
                    selected: isSelected,
                    selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.md),
                    ),
                    onTap: () => _onNavItemTapped(index),
                  ),
                );
              },
            ),
          ),
          _buildSideNavigationFooter(),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        if (state is Authenticated) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppRadius.lg),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: const Icon(
                    AppIcons.kepalaDapur,
                    color: AppColors.textOnPrimary,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        state.user.nama ?? '',
                        style: AppTypography.labelLarge.copyWith(
                          fontWeight: AppTypography.semiBold,
                        ),
                      ),
                      Text(
                        state.user.roleDisplayName,
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        state.user.sppgName ?? '',
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSideNavigationFooter() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        children: [
          AppButtonFactory.outline(
            text: 'Settings',
            onPressed: () => AppRouter.goToUserManagement(context),
            icon: AppIcons.settings,
            isFullWidth: true,
            size: AppButtonSize.small,
          ),
          const SizedBox(height: AppSpacing.sm),
          AppButtonFactory.text(
            text: 'Logout',
            onPressed: _logout,
            icon: AppIcons.logout,
            isFullWidth: true,
            size: AppButtonSize.small,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return BottomNavigationBar(
      currentIndex: _selectedNavIndex,
      onTap: _onNavItemTapped,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.neutralGray500,
      items: _navItems.map((item) {
        return BottomNavigationBarItem(
          icon: Icon(item.icon),
          label: item.label,
        );
      }).toList(),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _quickActions,
      icon: const Icon(AppIcons.add),
      label: const Text('Quick Action'),
      backgroundColor: AppColors.secondary,
    );
  }

  Widget _buildDashboardContent() {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, authState) {
        if (authState is Authenticated) {
          return BlocBuilder<DashboardCubit, DashboardState>(
            builder: (context, dashboardState) {
              if (dashboardState is DashboardLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (dashboardState is DashboardLoaded) {
                return SingleChildScrollView(
                  padding: EdgeInsets.all(
                    AppBreakpoints.getResponsivePadding(
                      MediaQuery.of(context).size.width,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildWelcomeCard(authState.user),
                      const SizedBox(height: AppSpacing.lg),
                      _buildRoleSpecificContent(
                          authState.user, dashboardState.summary),
                    ],
                  ),
                );
              } else if (dashboardState is DashboardError) {
                return Center(child: Text(dashboardState.message));
              }
              return const SizedBox.shrink();
            },
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildRoleSpecificContent(
      AppUser user, DashboardSummary summary) {
    switch (user.role) {
      case 'kepala_dapur':
        return _buildKepalaDapurDashboard(summary);
      case 'admin_yayasan':
        return _buildAdminYayasanDashboard();
      case 'ahli_gizi':
        return _buildAhliGiziDashboard();
      case 'akuntan':
        return _buildAkuntanDashboard();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildWelcomeCard(AppUser user) {
    return AppCardFactory.header(
      title: 'Selamat Datang, ${user.nama}!',
      subtitle: 'Dashboard Operasional Dapur ${user.sppgName ?? ''}',
      leading: const Icon(
        AppIcons.kepalaDapur,
        color: AppColors.primary,
        size: AppIcons.sizeLarge,
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: AppColors.successGreen.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppRadius.sm),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              AppIcons.online,
              size: AppIcons.sizeSmall,
              color: AppColors.successGreen,
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              'Online',
              style: AppTypography.labelSmall.copyWith(
                color: AppColors.successGreen,
              ),
            ),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Operasional hari ini berjalan lancar. Status dapur: Aktif',
            style: AppTypography.bodyMedium,
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            children: [
              AppButtonFactory.primary(
                text: 'Mulai Operasional',
                onPressed: () => AppRouter.goToKitchenManagement(context),
                icon: AppIcons.cooking,
                size: AppButtonSize.medium,
              ),
              const SizedBox(width: AppSpacing.sm),
              AppButtonFactory.outline(
                text: 'Lihat Laporan',
                onPressed: () => AppRouter.goToReporting(context),
                icon: AppIcons.report,
                size: AppButtonSize.medium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKepalaDapurDashboard(DashboardSummary summary) {
    return Column(
      children: [
        _buildQuickStats(summary),
        const SizedBox(height: AppSpacing.lg),
        const TaskListView(),
        const SizedBox(height: AppSpacing.lg),
        _buildRecentActivities(),
      ],
    );
  }

  Widget _buildAdminYayasanDashboard() {
    // Implement Admin Yayasan Dashboard
    return Column(
      children: [
        const ApprovalRequestItem(),
        const SizedBox(
          height: AppSpacing.lg,
        ),
        _buildRecentActivities(),
      ],
    );
  }

  Widget _buildAhliGiziDashboard() {
    // Implement Ahli Gizi Dashboard
    return Column(
      children: [
        _buildRecentActivities(),
      ],
    );
  }

  Widget _buildAkuntanDashboard() {
    // Implement Akuntan Dashboard
    return Column(
      children: [
        _buildRecentActivities(),
      ],
    );
  }

  Widget _buildQuickStats(DashboardSummary summary) {
    return Row(
      children: [
        Expanded(
          child: SummaryCard(
            title: 'Menu Hari Ini',
            value: summary.totalPorsi.toString(),
            subtitle: 'porsi',
            icon: AppIcons.localDining,
            color: AppColors.primary,
            trend: '+12%',
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: SummaryCard(
            title: 'Stock Bahan',
            value: '85%',
            subtitle: 'tersedia',
            icon: AppIcons.inventory,
            color: AppColors.successGreen,
            trend: '-3%',
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: SummaryCard(
            title: 'Pengiriman',
            value: summary.jadwalPengiriman.toString(),
            subtitle: 'rute',
            icon: AppIcons.delivery,
            color: AppColors.secondary,
            trend: '+8%',
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: SummaryCard(
            title: 'Budget',
            value: '68%',
            subtitle: 'terpakai',
            icon: AppIcons.budget,
            color: AppColors.infoBlue,
            trend: '+5%',
          ),
        ),
      ],
    );
  }

  Widget _buildRecentActivities() {
    return AppCardFactory.header(
      title: 'Aktivitas Terbaru',
      child: Column(
        children: [
          _buildActivityItem(
            icon: AppIcons.stockIn,
            title: 'Penerimaan bahan baku',
            subtitle: 'Beras 50kg, Ayam 30kg diterima',
            time: '2 jam lalu',
            color: AppColors.successGreen,
          ),
          _buildActivityItem(
            icon: AppIcons.qualityControl,
            title: 'QC Passed',
            subtitle: 'Menu hari ini lolos quality control',
            time: '3 jam lalu',
            color: AppColors.primary,
          ),
          _buildActivityItem(
            icon: AppIcons.delivery,
            title: 'Pengiriman selesai',
            subtitle: 'Rute A1-A5 berhasil dikirim',
            time: '1 hari lalu',
            color: AppColors.infoBlue,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppRadius.md),
            ),
            child: Icon(
              icon,
              color: color,
              size: AppIcons.sizeMedium,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.labelLarge,
                ),
                Text(
                  subtitle,
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  time,
                  style: AppTypography.labelSmall.copyWith(
                    color: AppColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Placeholder content untuk halaman lain
  Widget _buildKitchenContent() {
    return const Center(
      child: Text('Kitchen Management Content'),
    );
  }

  Widget _buildInventoryContent() {
    return const Center(
      child: Text('Inventory Content'),
    );
  }

  Widget _buildLogisticsContent() {
    return const Center(
      child: Text('Logistics Content'),
    );
  }

  Widget _buildFinancialContent() {
    return const Center(
      child: Text('Financial Content'),
    );
  }

  Widget _buildReportsContent() {
    return const Center(
      child: Text('Reports Content'),
    );
  }

  // Event handlers
  void _onNavItemTapped(int index) {
    setState(() {
      _selectedNavIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _showNotifications() {
    _logger.d('Show notifications');
    // TODO: Implement notifications
  }

  void _syncData() {
    _logger.d('Sync data');
    // TODO: Implement data sync
  }

  void _showProfile() {
    _logger.d('Show profile');
    // TODO: Implement profile page
  }

  void _quickActions() {
    _logger.d('Quick actions');
    // TODO: Implement quick actions
  }

  void _logout() {
    _logger.d('Logout');
    context.read<AuthCubit>().signOut();
    AppRouter.goToLogin(context);
  }
}

class DashboardNavItem {
  final IconData icon;
  final String label;
  final String route;

  DashboardNavItem({
    required this.icon,
    required this.label,
    required this.route,
  });
}
