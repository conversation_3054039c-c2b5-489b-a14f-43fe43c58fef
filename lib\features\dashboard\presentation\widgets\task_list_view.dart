import 'package:flutter/material.dart';

class TaskListView extends StatelessWidget {
  const TaskListView({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: const [
        ListTile(
          leading: Icon(Icons.check_circle, color: Colors.green),
          title: Text('Persiapan Menu'),
          subtitle: Text('Nasi + <PERSON><PERSON> + <PERSON><PERSON>'),
          trailing: Text('Selesai'),
        ),
        ListTile(
          leading: Icon(Icons.hourglass_bottom, color: Colors.orange),
          title: Text('Proses Memasak'),
          subtitle: Text('750 porsi sedang diproses'),
          trailing: Text('Berlangsung'),
        ),
        ListTile(
          leading: Icon(Icons.watch_later, color: Colors.grey),
          title: Text('Quality Control'),
          subtitle: Text('Pemeriksaan kualitas makanan'),
          trailing: Text('<PERSON>unggu'),
        ),
      ],
    );
  }
}
