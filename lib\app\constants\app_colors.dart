import 'package:flutter/material.dart';

/// Color palette untuk Aplikasi SOD-MBG (Sistem Operasional Dapur MBG)
/// Menggunakan skema warna "YellowBlueSkyHappy" yang mencerminkan kehangatan dan ketenangan
class AppColors {
  // Private constructor untuk mencegah instantiation
  AppColors._();

  // ===== PRIMARY COLORS - YellowBlueSkyHappy Theme =====
  /// Warna utama - biru tengah yang menenangkan namun profesional
  static const Color primary = Color(0xFF749BC2); // rgb(116, 155, 194)
  static const Color primaryLight = Color(0xFF91C8E4); // rgb(145, 200, 228)
  static const Color primaryDark = Color(0xFF4682A9); // rgb(70, 130, 169)

  /// Warna sekunder - kuning krem hangat untuk aksen dan kehangatan
  static const Color secondary = Color(0xFFFFFBDE); // rgb(255, 251, 222)
  static const Color secondaryLight = Color(0xFFFFFEF0); // Lighter version
  static const Color secondaryDark = Color(0xFFF5F1C7); // Darker version

  // ===== LEGACY COLOR ALIASES (untuk backward compatibility) =====
  /// Alias untuk transisi bertahap - akan dihapus nanti
  static const Color primaryGreen = primary; // Gunakan primary
  static const Color primaryGreenLight = primaryLight;
  static const Color primaryGreenDark = primaryDark;
  static const Color secondaryOrange = Color(0xFFF5C775); // Adjusted to fit theme
  static const Color secondaryOrangeLight = Color(0xFFF8D89A);
  static const Color secondaryOrangeDark = Color(0xFFE6B85C);

  // ===== NEUTRAL COLORS =====
  /// Warna netral disesuaikan dengan tema YellowBlueSkyHappy
  static const Color neutralWhite = Color(0xFFFFFFFF);
  static const Color neutralCream = secondary; // Gunakan secondary cream
  static const Color neutralGray50 = Color(0xFFF8F9FA);
  static const Color neutralGray100 = Color(0xFFF1F3F4);
  static const Color neutralGray200 = Color(0xFFE8EAED);
  static const Color neutralGray300 = Color(0xFFDADCE0);
  static const Color neutralGray400 = Color(0xFFBDC1C6);
  static const Color neutralGray500 = Color(0xFF9AA0A6);
  static const Color neutralGray600 = Color(0xFF80868B);
  static const Color neutralGray700 = Color(0xFF5F6368);
  static const Color neutralGray800 = Color(0xFF3C4043);
  static const Color neutralGray900 = Color(0xFF202124);

  // ===== SEMANTIC COLORS =====
  /// Warna untuk status dan feedback - disesuaikan dengan tema
  static const Color successGreen = Color(0xFF34A853); // Google Green
  static const Color warningYellow = Color(0xFFFBBC05); // Google Yellow, selaras dengan secondary
  static const Color errorRed = Color(0xFFEA4335); // Google Red
  static const Color infoBlue = primaryLight; // Gunakan primary light blue
  
  // Aliases untuk konsistensi
  static const Color success = successGreen;
  static const Color warning = warningYellow;
  static const Color error = errorRed;
  static const Color info = infoBlue;
  
  // Light variants
  static const Color successLight = Color(0xFFE8F5E8);
  static const Color warningLight = Color(0xFFFFF8E0);
  static const Color errorLight = Color(0xFFFFEBEE);
  static const Color infoLight = Color(0xFFE3F2FD);

  // ===== ROLE-BASED COLORS =====
  /// Warna khusus untuk setiap role user - disesuaikan dengan tema YellowBlueSkyHappy
  static const Color adminYayasanColor = primaryDark; // Biru tua untuk otoritas
  static const Color perwakilanYayasanColor = primary; // Biru sedang untuk supervisor
  static const Color kepalaDapurColor = Color(0xFF5B9BD5); // Biru kitchen-friendly
  static const Color ahliGiziColor = Color(0xFF70AD47); // Hijau selaras dengan tema
  static const Color akuntanColor = Color(0xFF4BACC6); // Cyan biru selaras
  static const Color pengawasPemeliharaanColor = Color(0xFFF79646); // Orange hangat selaras

  // ===== BACKGROUND COLORS =====
  /// Background disesuaikan dengan tema YellowBlueSkyHappy
  static const Color backgroundPrimary = neutralWhite;
  static const Color backgroundSecondary = secondary; // Cream hangat
  static const Color backgroundTertiary = neutralGray50;
  static const Color surfaceColor = neutralWhite;
  static const Color cardBackground = neutralWhite;

  // ===== TEXT COLORS =====
  /// Warna teks dengan hierarki yang jelas
  static const Color textPrimary = neutralGray900;
  static const Color textSecondary = neutralGray700;
  static const Color textTertiary = neutralGray500;
  static const Color textOnPrimary = neutralWhite;
  static const Color textOnSecondary = neutralGray900;

  // ===== KITCHEN OPERATIONAL COLORS =====
  /// Warna khusus untuk operasional dapur - tema YellowBlueSkyHappy
  static const Color kitchenClean = Color(0xFFE8F4FD); // Light blue untuk area bersih
  static const Color kitchenCooking = secondaryLight; // Cream terang untuk area memasak
  static const Color kitchenStorage = Color(0xFFE1F0F8); // Very light blue untuk penyimpanan
  static const Color kitchenDanger = Color(0xFFFCE8E6); // Light red untuk area berbahaya

  // ===== INVENTORY STATUS COLORS =====
  /// Warna untuk status inventory - disesuaikan tema
  static const Color stockAvailable = successGreen;
  static const Color stockLow = warningYellow;
  static const Color stockEmpty = errorRed;
  static const Color stockExpiring = Color(0xFFFF8A65); // Orange selaras tema

  // ===== QC STATUS COLORS =====
  /// Warna untuk Quality Control - disesuaikan tema
  static const Color qcPassed = successGreen;
  static const Color qcWarning = warningYellow;
  static const Color qcFailed = errorRed;
  static const Color qcPending = neutralGray500;

  // ===== DISTRIBUTION COLORS =====
  /// Warna untuk status distribusi - disesuaikan tema
  static const Color distributionReady = successGreen;
  static const Color distributionInTransit = primaryLight;
  static const Color distributionDelivered = Color(0xFF81C784);
  static const Color distributionDelayed = Color(0xFFFFB74D);
}

/// Extension untuk ColorScheme yang disesuaikan dengan kebutuhan aplikasi
extension AppColorScheme on ColorScheme {
  /// Mendapatkan warna berdasarkan role user
  Color getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin_yayasan':
        return AppColors.adminYayasanColor;
      case 'perwakilan_yayasan':
        return AppColors.perwakilanYayasanColor;
      case 'kepala_dapur':
        return AppColors.kepalaDapurColor;
      case 'ahli_gizi':
        return AppColors.ahliGiziColor;
      case 'akuntan':
        return AppColors.akuntanColor;
      case 'pengawas_pemeliharaan':
        return AppColors.pengawasPemeliharaanColor;
      default:
        return AppColors.primary; // Gunakan primary bukan primaryGreen
    }
  }

  /// Mendapatkan warna berdasarkan status inventory
  Color getInventoryStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return AppColors.stockAvailable;
      case 'low':
        return AppColors.stockLow;
      case 'empty':
        return AppColors.stockEmpty;
      case 'expiring':
        return AppColors.stockExpiring;
      default:
        return AppColors.neutralGray500;
    }
  }

  /// Mendapatkan warna berdasarkan status QC
  Color getQcStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'passed':
        return AppColors.qcPassed;
      case 'warning':
        return AppColors.qcWarning;
      case 'failed':
        return AppColors.qcFailed;
      case 'pending':
        return AppColors.qcPending;
      default:
        return AppColors.neutralGray500;
    }
  }
}
