import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/dashboard_repository.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/dashboard_summary.dart';

class SupabaseDashboardRepository implements DashboardRepository {
  final SupabaseService _supabaseService;

  SupabaseDashboardRepository(this._supabaseService);

  @override
  Future<DashboardSummary> getDashboardSummary(String role) async {
    try {
      final response = await _supabaseService.client
          .rpc('get_dashboard_summary', params: {'role': role});

      return DashboardSummary(
        totalPorsi: response['total_porsi'] as int,
        jadwalPengiriman: response['jadwal_pengiriman'] as int,
        statusQc: response['status_qc'] as String,
      );
    } catch (e) {
      // Handle error
      rethrow;
    }
  }
}
