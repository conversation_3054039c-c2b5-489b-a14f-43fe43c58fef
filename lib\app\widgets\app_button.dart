import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';

/// Custom button component untuk Aplikasi SOD-MBG
/// Menggunakan design system yang konsisten
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Color? customColor;

  static final Logger _logger = Logger();

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
  });

  @override
  Widget build(BuildContext context) {
    _logger.d('Building AppButton: $text, type: $type, size: $size');

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Determine button style based on type
    Widget button;
    switch (type) {
      case AppButtonType.primary:
        button = _buildPrimaryButton(context, colorScheme);
        break;
      case AppButtonType.secondary:
        button = _buildSecondaryButton(context, colorScheme);
        break;
      case AppButtonType.outline:
        button = _buildOutlineButton(context, colorScheme);
        break;
      case AppButtonType.text:
        button = _buildTextButton(context, colorScheme);
        break;
      case AppButtonType.danger:
        button = _buildDangerButton(context, colorScheme);
        break;
    }

    return isFullWidth 
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  Widget _buildPrimaryButton(BuildContext context, ColorScheme colorScheme) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: customColor ?? colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        padding: _getPadding(),
        textStyle: _getTextStyle(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildSecondaryButton(BuildContext context, ColorScheme colorScheme) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: customColor ?? colorScheme.secondary,
        foregroundColor: colorScheme.onSecondary,
        padding: _getPadding(),
        textStyle: _getTextStyle(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlineButton(BuildContext context, ColorScheme colorScheme) {
    return OutlinedButton(
      onPressed: isLoading ? null : onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: customColor ?? colorScheme.primary,
        padding: _getPadding(),
        textStyle: _getTextStyle(),
        side: BorderSide(
          color: customColor ?? colorScheme.primary,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildTextButton(BuildContext context, ColorScheme colorScheme) {
    return TextButton(
      onPressed: isLoading ? null : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: customColor ?? colorScheme.primary,
        padding: _getPadding(),
        textStyle: _getTextStyle(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildDangerButton(BuildContext context, ColorScheme colorScheme) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: customColor ?? AppColors.errorRed,
        foregroundColor: AppColors.textOnPrimary,
        padding: _getPadding(),
        textStyle: _getTextStyle(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        height: _getIconSize(),
        width: _getIconSize(),
        child: const CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.textOnPrimary),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          const SizedBox(width: AppSpacing.sm),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.xs,
        );
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppSpacing.lg,
          vertical: AppSpacing.sm,
        );
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppSpacing.xl,
          vertical: AppSpacing.md,
        );
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case AppButtonSize.small:
        return AppTypography.buttonSmall;
      case AppButtonSize.medium:
        return AppTypography.buttonMedium;
      case AppButtonSize.large:
        return AppTypography.buttonLarge;
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return AppIcons.sizeSmall;
      case AppButtonSize.medium:
        return AppIcons.sizeMedium;
      case AppButtonSize.large:
        return AppIcons.sizeLarge;
    }
  }
}

enum AppButtonType {
  primary,
  secondary,
  outline,
  text,
  danger,
}

enum AppButtonSize {
  small,
  medium,
  large,
}

/// Factory constructors untuk kemudahan penggunaan
class AppButtonFactory {
  // Private constructor
  AppButtonFactory._();

  /// Primary button
  static AppButton primary({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return AppButton(
      text: text,
      onPressed: onPressed,
      type: AppButtonType.primary,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Secondary button
  static AppButton secondary({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return AppButton(
      text: text,
      onPressed: onPressed,
      type: AppButtonType.secondary,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Outline button
  static AppButton outline({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return AppButton(
      text: text,
      onPressed: onPressed,
      type: AppButtonType.outline,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Text button
  static AppButton text({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return AppButton(
      text: text,
      onPressed: onPressed,
      type: AppButtonType.text,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Danger button
  static AppButton danger({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    AppButtonSize size = AppButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return AppButton(
      text: text,
      onPressed: onPressed,
      type: AppButtonType.danger,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }
}
