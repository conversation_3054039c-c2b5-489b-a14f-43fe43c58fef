import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_card.dart';

/// Demo page untuk menampilkan implementasi skema warna YellowBlueSkyHappy
class ColorSchemeDemoPage extends StatefulWidget {
  const ColorSchemeDemoPage({super.key});

  @override
  State<ColorSchemeDemoPage> createState() => _ColorSchemeDemoPageState();
}

class _ColorSchemeDemoPageState extends State<ColorSchemeDemoPage> {
  final Logger _logger = Logger();

  @override
  void initState() {
    super.initState();
    _logger.i('ColorSchemeDemoPage initialized');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Demo Skema Warna YellowBlueSkyHappy'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildHeader(),
              _buildColorPalette(),
              _buildComponentExamples(),
              _buildRoleColors(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'YellowBlueSkyHappy',
            style: AppTypography.h4.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Skema warna baru yang mengkombinasikan kehangatan kuning krem dengan ketenangan biru langit',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorPalette() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Palet Warna Utama',
            style: AppTypography.h5,
          ),
          const SizedBox(height: AppSpacing.md),
          
          // Primary Colors
          _buildColorSection('Primary Colors', [
            _ColorItem('Primary', AppColors.primary, '#749BC2'),
            _ColorItem('Primary Light', AppColors.primaryLight, '#91C8E4'),
            _ColorItem('Primary Dark', AppColors.primaryDark, '#4682A9'),
          ]),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Secondary Colors
          _buildColorSection('Secondary Colors', [
            _ColorItem('Secondary', AppColors.secondary, '#FFFBDE'),
            _ColorItem('Secondary Light', AppColors.secondaryLight, '#FFFEF0'),
            _ColorItem('Secondary Dark', AppColors.secondaryDark, '#F5F1C7'),
          ]),
        ],
      ),
    );
  }

  Widget _buildColorSection(String title, List<_ColorItem> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.h6.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: colors.map((color) => _buildColorCard(color)).toList(),
        ),
      ],
    );
  }

  Widget _buildColorCard(_ColorItem colorItem) {
    return Container(
      width: 120,
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: colorItem.color,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.neutralGray300,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: colorItem.color,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            colorItem.name,
            style: AppTypography.labelSmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            colorItem.hex,
            style: AppTypography.labelSmall.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildComponentExamples() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contoh Komponen',
            style: AppTypography.h5,
          ),
          const SizedBox(height: AppSpacing.md),
          
          // Buttons
          AppCardFactory.basic(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Buttons',
                  style: AppTypography.h6,
                ),
                const SizedBox(height: AppSpacing.md),
                Wrap(
                  spacing: AppSpacing.sm,
                  runSpacing: AppSpacing.sm,
                  children: [
                    AppButtonFactory.primary(
                      text: 'Primary',
                      onPressed: () {},
                    ),
                    AppButtonFactory.secondary(
                      text: 'Secondary',
                      onPressed: () {},
                    ),
                    AppButtonFactory.outline(
                      text: 'Outline',
                      onPressed: () {},
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Status Colors
          AppCardFactory.basic(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Status Colors',
                  style: AppTypography.h6,
                ),
                const SizedBox(height: AppSpacing.md),
                _buildStatusExample('Success', AppColors.successGreen),
                _buildStatusExample('Warning', AppColors.warningYellow),
                _buildStatusExample('Error', AppColors.errorRed),
                _buildStatusExample('Info', AppColors.infoBlue),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusExample(String label, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Text(
            label,
            style: AppTypography.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildRoleColors() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Warna Role User',
            style: AppTypography.h5,
          ),
          const SizedBox(height: AppSpacing.md),
          
          AppCardFactory.basic(
            child: Column(
              children: [
                _buildRoleExample('Admin Yayasan', AppColors.adminYayasanColor),
                _buildRoleExample('Perwakilan Yayasan', AppColors.perwakilanYayasanColor),
                _buildRoleExample('Kepala Dapur', AppColors.kepalaDapurColor),
                _buildRoleExample('Ahli Gizi', AppColors.ahliGiziColor),
                _buildRoleExample('Akuntan', AppColors.akuntanColor),
                _buildRoleExample('Pengawas Pemeliharaan', AppColors.pengawasPemeliharaanColor),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleExample(String role, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              role,
              style: AppTypography.bodyMedium.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ColorItem {
  final String name;
  final Color color;
  final String hex;

  _ColorItem(this.name, this.color, this.hex);
}
