# Supabase Setup Guide for SOD-MBG Application

## Quick Setup

The application currently uses placeholder Supabase credentials and will show an error when trying to use authentication features. Follow these steps to set up proper Supabase integration:

### 1. Create a Supabase Project

1. Go to [Supabase Dashboard](https://app.supabase.com/)
2. Create a new project
3. Wait for the project to be fully initialized

### 2. Get Your Credentials

From your Supabase dashboard:
1. Go to **Settings** → **API**
2. Copy the **Project URL** and **anon public key**

### 3. Configure Environment Variables

Create a `.env` file in the root directory (copy from `.env.example`):

```bash
# Your actual Supabase credentials
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-actual-anon-key
ENVIRONMENT=development
```

### 4. Alternative: Update Configuration Directly

If you prefer not to use environment variables, you can update the configuration directly in:
`lib/core/config/supabase_config.dart`

Replace the placeholder values:
```dart
static const String supabaseUrl = 'https://your-project-id.supabase.co';
static const String supabaseAnonKey = 'your-actual-anon-key';
```

### 5. Database Schema Setup

The application expects the following tables in your Supabase database:

#### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  nama TEXT NOT NULL,
  role TEXT NOT NULL,
  sppg_id TEXT,
  sppg_name TEXT,
  is_anonymous BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}'::jsonb
);
```

#### Enable Row Level Security
```sql
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to read their own data
CREATE POLICY "Users can read own data" ON users
  FOR SELECT USING (auth.uid() = id);

-- Policy for authenticated users to update their own data
CREATE POLICY "Users can update own data" ON users
  FOR UPDATE USING (auth.uid() = id);
```

### 6. Edge Functions (Optional)

For admin yayasan registration, you may need to create an Edge Function:

```typescript
// supabase/functions/register-admin-yayasan/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, password, nama, namaYayasan } = await req.json()
    
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        nama,
        role: 'admin_yayasan',
        sppg_name: namaYayasan,
      }
    })

    if (authError) throw authError

    // Insert user data into users table
    const { error: userError } = await supabaseAdmin
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        nama,
        role: 'admin_yayasan',
        sppg_name: namaYayasan,
      })

    if (userError) throw userError

    return new Response(
      JSON.stringify({ success: true, user: authData.user }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    )
  }
})
```

### 7. Testing

After setup, run the application:

```bash
flutter run
```

The application should now:
- Initialize Supabase successfully
- Show proper error messages if configuration is missing
- Allow registration and login functionality

## Development Mode

For development without Supabase:
- The application will run in offline mode
- Authentication features will be limited
- Use the demo pages to test UI components

## Troubleshooting

**Error: "Supabase configuration is invalid"**
- Check that SUPABASE_URL and SUPABASE_ANON_KEY are properly set
- Verify the URL format: `https://your-project-id.supabase.co`
- Ensure the anon key is copied correctly from Supabase dashboard

**Error: "You must initialize the supabase instance"**
- This error occurs when trying to use auth features without proper configuration
- Follow the setup steps above to configure Supabase properly

**Registration/Login fails**
- Check that the users table exists in your Supabase database
- Verify Row Level Security policies are set up correctly
- Check Supabase logs for detailed error messages
