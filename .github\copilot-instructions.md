## Project Overview
This is a **Flutter cross-platform application** called **Sistem Operasional Dapur MBG (SOD-MBG)** - Kitchen Operational System for the Free Nutritious Meals (MBG) program. The application is specifically designed to support daily operations of SPPG (Satuan Pelayanan Pemenuhan Gizi) kitchens in implementing the MBG program from Badan Gizi Nasional (BGN).

## Business Context & Organization
- **Organization:** Foundation-based initiative (Yayasan) managing MBG program
- **Primary Focus:** Kitchen operational management and oversight
- **Target Users:** Foundation admins, kitchen managers, nutritionists, accountants, logistics supervisors
- **Purpose:** Improve internal efficiency, ensure accurate reporting, provide strict oversight tools for foundation over SPPG kitchens (both owned and partner non-foundation kitchens)

## Current Project Status
The project has **completed Phase 1** with comprehensive UI/UX implementation including:
- Complete design system with YellowBlueSkyHappy color scheme
- Role-based navigation drawer with 6 user roles
- Responsive layouts for desktop, tablet, and mobile
- Comprehensive component library and widget system
- Feature-based architecture with placeholder implementations
- Cross-platform support (Android, iOS, Web, Windows, macOS, Linux)
- Offline-first architecture using PowerSync (configured but not implemented)
- Supabase backend integration (configured but not implemented)

## Architecture & Design System
- **Color Scheme:** YellowBlueSkyHappy theme with `AppColors.primary` (blue #749BC2) and `AppColors.secondary` (cream #FFFBDE)
- **Typography:** Complete typography system in `AppTypography` with kitchen-specific variants (kitchenDisplay, kitchenCounter, kitchenStatus)
- **Responsive Design:** Uses `AppBreakpoints` for mobile/tablet/desktop layouts with `ResponsiveLayout` widgets
- **Component Library:** Comprehensive widget system with factories (`AppButtonFactory`, `AppCardFactory`) in `lib/app/widgets/`
- **Navigation:** GoRouter-based routing with centralized `AppRouter` class containing all route definitions and helper methods
- **State Management:** Planned BLoC pattern (not yet implemented)
- **Theming:** Material 3 with custom themes including specialized `kitchenTheme` for high-visibility displays

## Development Patterns & Conventions
- **Feature-based Structure:** `lib/features/` contains domain-specific code (auth, dashboard, admin, delivery, qc, demo)
- **Mandatory Logger:** Every class must use `final Logger _logger = Logger();` and log operations with context
- **Navigation:** Use `AppRouter.goTo*()` methods, never direct `context.go()` calls
- **Responsive Widgets:** Prefer `ResponsiveLayout`, `ResponsivePadding`, `ResponsiveGrid` over manual MediaQuery
- **Color Usage:** Always use `AppColors` constants, never hardcoded colors
- **Icons:** Use `AppIcons` constants for consistency
- **Spacing:** Use `AppSpacing` constants instead of hardcoded values

## Component Architecture
- **App Widgets:** Reusable components in `lib/app/widgets/` with factory patterns
- **Role-based Navigation:** `SodNavigationDrawer` dynamically shows menu based on user role
- **Workflow Components:** Kitchen-specific components in `workflow_components.dart`
- **State Components:** Loading, error, empty states in `app_states.dart`
- **Form Components:** Validation-ready forms in `app_form.dart`

## Role-Based Access Control (RBAC)
The application implements 6 main user roles with different menu access:

1. **Admin Yayasan:** Full system access. Manages SPPG, users, and final report approvals
2. **Perwakilan Yayasan:** Operational supervisor. Verifies daily reports, conducts digital audits, manages incidents
3. **Kepala Dapur SPPG:** Kitchen manager. Manages daily operations, staff, QC, and compiles daily reports
4. **Ahli Gizi:** Nutritionist. Manages menu cycles and validates daily nutritional compliance
5. **Akuntan:** Accountant. Records transactions, performs reconciliation, compiles SPPG financial reports
6. **Pengawas Pemeliharaan & Penghantaran:** Logistics supervisor. Manages distribution, fleet, and maintenance reporting

## Technical Stack & Dependencies
- **Framework:** Flutter 3.7.2+ (cross-platform: Android, iOS, Web, Windows, macOS, Linux)
- **Backend:** Supabase (PostgreSQL database) - configured but not implemented
- **Offline-First:** PowerSync for offline synchronization - configured but not implemented
- **State Management:** BLoC pattern (planned)
- **Routing:** GoRouter 7.0.0 with centralized route management
- **UI:** Material 3 with custom YellowBlueSkyHappy theme
- **Logging:** Logger 2.6.0 (mandatory for all development)
- **Desktop:** desktop_window 0.4.2 for fullscreen support

## Current Implementation Status
### ✅ Completed (Phase 1)
- Complete design system and UI components
- Role-based navigation drawer
- Responsive layouts for all screen sizes
- Comprehensive widget library
- Feature page scaffolding with placeholder implementations
- Theme system with kitchen-specific variants
- Router configuration with all planned routes

### 🔄 In Progress/Planned
- Business logic implementation
- Data models and repositories
- Offline synchronization with PowerSync
- Supabase backend integration
- State management with BLoC
- Testing coverage

## Key Development Commands
```bash
# Run the application
flutter run

# Desktop fullscreen mode (configured in main.dart)
flutter run -d windows --release

# Build for production
flutter build windows --release

# Analyze code (maintain clean analysis)
flutter analyze

# Test the app
flutter test
```

## Project Structure (Actual)
```
lib/
├── main.dart                   # Entry point with desktop window config
├── app/
│   ├── config/
│   │   ├── app_router.dart     # Complete routing with all routes
│   │   └── app_theme.dart      # Material 3 + custom themes
│   ├── constants/
│   │   ├── app_colors.dart     # YellowBlueSkyHappy color scheme
│   │   ├── app_icons.dart      # Icon constants
│   │   ├── app_spacing.dart    # Spacing system
│   │   ├── app_typography.dart # Typography with kitchen variants
│   │   └── app_radius.dart     # Border radius constants
│   └── widgets/
│       ├── app_button.dart     # Button component factory
│       ├── app_card.dart       # Card component factory
│       ├── app_form.dart       # Form components
│       ├── app_data_display.dart # Data display components
│       ├── app_navigation.dart # Navigation components
│       ├── app_notifications.dart # Notification system
│       ├── app_states.dart     # Loading/error/empty states
│       ├── responsive_layout.dart # Responsive design components
│       ├── sod_navigation_drawer.dart # Role-based nav drawer
│       └── workflow_components.dart # Kitchen workflow widgets
├── features/
│   ├── auth/presentation/pages/
│   │   ├── login_page.dart     # Login implementation
│   │   └── home_page.dart      # Responsive home page
│   ├── dashboard/presentation/pages/
│   │   └── dashboard_page.dart # Main dashboard with nav integration
│   ├── admin/presentation/pages/
│   │   └── admin_dashboard_page.dart # Admin-specific dashboard
│   ├── delivery/presentation/pages/
│   │   └── delivery_tracking_page.dart # Delivery tracking
│   ├── qc/presentation/pages/
│   │   └── qc_daily_page.dart  # Quality control daily
│   └── demo/presentation/pages/
│       ├── ui_workflow_demo_page.dart # UI/UX demo
│       └── color_scheme_demo_page.dart # Color scheme demo
```

## Critical Implementation Patterns

### Navigation Pattern
```dart
// ALWAYS use AppRouter helper methods, never direct context.go()
AppRouter.goToDashboard(context);
AppRouter.goToKitchenManagement(context);

// Check current route
if (AppRouter.isCurrentRoute(context, AppRouter.dashboard)) {
  // Handle dashboard-specific logic
}
```

### Component Factory Pattern
```dart
// Use factory methods for consistent components
AppButtonFactory.primary(text: 'Login', onPressed: () {});
AppCardFactory.header(title: 'Dashboard', child: content);
```

### Responsive Layout Pattern
```dart
// Use responsive widgets instead of manual MediaQuery
ResponsiveLayout(
  mobile: mobileWidget,
  tablet: tabletWidget,
  desktop: desktopWidget,
)

// Use responsive breakpoints
final padding = AppBreakpoints.getResponsivePadding(screenWidth);
```

### Logger Pattern (Mandatory)
```dart
class MyClass {
  final Logger _logger = Logger();
  
  void myMethod() {
    _logger.d('Executing myMethod');
    try {
      // Implementation
      _logger.i('myMethod completed successfully');
    } catch (e) {
      _logger.e('Error in myMethod: $e');
    }
  }
}
```

### Role-Based UI Pattern
```dart
// Use role-based navigation drawer
SodNavigationDrawer(
  userName: 'Ahmad Suharto',
  userRole: 'kepala_dapur', // Determines menu visibility
  sppgName: 'SPPG Jakarta Pusat',
  onItemSelected: (index) => handleNavigation(index),
)

// Get role-specific colors
final roleColor = Theme.of(context).colorScheme.getRoleColor(userRole);
```

## Integration Points & Dependencies
- **Desktop Window Management:** Configured in `main.dart` with fullscreen support
- **Theme Integration:** All components use `AppTheme` with YellowBlueSkyHappy scheme
- **Router Integration:** All pages configured in `AppRouter` with helper methods
- **Responsive System:** `AppBreakpoints` used throughout for consistent responsive behavior
- **Component Library:** Widget factories in `lib/app/widgets/` for consistent UI

## Development Workflow
1. **Feature Development:** Create in `lib/features/[feature_name]/presentation/pages/`
2. **Component Development:** Add to `lib/app/widgets/` with factory pattern
3. **Navigation:** Add routes to `AppRouter` and create helper methods
4. **Testing:** Test on desktop, tablet, and mobile breakpoints
5. **Logging:** Add logging to all new classes and methods

## Business Logic Focus Areas
- **Kitchen Operations:** Daily workflow management from planning to delivery
- **Inventory Control:** Real-time stock management with offline capability
- **Quality Control:** QC processes and compliance tracking
- **Financial Tracking:** Transaction recording and budget management
- **Logistics Management:** Distribution, fleet, and equipment management
- **Multi-level Reporting:** Kitchen, foundation, and BGN portal integration
- **Foundation Oversight:** Monitoring and control tools for kitchen supervision

## Development Guidelines
When providing suggestions or implementations, always consider:
- **Offline-first requirements:** All core functions must work without internet
- **Kitchen workflow optimization:** Focus on practical kitchen operations
- **Multi-platform compatibility:** Ensure consistent experience across devices
- **Feature-based architecture:** Organize code by business features
- **Foundation context:** Non-governmental organization with oversight needs
- **SPPG operational needs:** Practical requirements of kitchen managers
- **Scalability:** Design for national deployment across multiple kitchens
- **User experience:** Consider varying technology literacy levels
- **Compliance:** Ensure compatibility with BGN reporting requirements
- **Use Logger:** Always use the `logger` package for debugging, error handling, and operational logging to ensure traceability and maintainability.